import json
import logging
import traceback
import math

from django.db import models
from fields.models import Field
from django.core.validators import RegexValidator, MinValueValidator
from django.dispatch import receiver
from django.db.models.signals import post_save
from channels.layers import get_channel_layer
from django.utils import timezone
from django.utils.timesince import timesince
from concurrent.futures import ThreadPoolExecutor
from decouple import config
# Import async_to_sync only where needed


# Import async_to_sync only where needed



logger = logging.getLogger("app")
executor = ThreadPoolExecutor(max_workers=config("THREADPOOL_SIZE", cast=int))

attributes_templates = {
    "Whiskers Gateway V1": {
        "attr": {
            "client": {
                "temp": 0,
                "txok": 0,
                "txin": 0,
                "ackr": 0,
                "rxok": 0,
                "rxfw": 0,
                "IP": "0.0.0.0",
            },
            "server": {},
            "shared": {},
        },
        "tels": ["temp"],
        "conc": {
            "temp": "temp",
        },
    },
    "Whiskers Node V1": {
        "attr": {
            "client": {
                "Air Temperature": 0,
                "Battery": 0,
                "Light": 0,
                "Positioning Status": "",
                "Frame Counter": 0,
                "Motion event.": False,
                "Motionless event.": False,
                "Shock event.": False,
                "RSSI": 0,
            },
            "server": {},
            "shared": {},
        },
        "tels": [
            "Air Temperature",
            "Battery",
            "Light",
            "Shock event.",
            "Motion event.",
            "Motionless event.",
        ],
        "conc": {
            "temp": "Air Temperature",
            "batt": "Battery",
            "loca": {
                "lati": "Latitude",
                "long": "Longitude",
            },
        },
    },
}


# default attributes_templates for the device
def get_device_attrs_template(type):
    return attributes_templates[type]["attr"] if type in attributes_templates else {}


def get_location_template():
    return {
        "lati": 0,
        "long": 0,
        "alti": 0,
        "oofi": False,
        "plac": "Outdoor",
    }


eui_validator = RegexValidator(
    regex=r"^[A-Fa-f0-9]{16}$",
    message="Dev key should only consist of (A-F) and (0-9) and be 16 characters long.",
)


def calculate_speed_and_direction(lat1, lng1, lat2, lng2, time_interval_seconds):
    """Calculate speed in km/h and direction in degrees between two points"""
    # If coordinates are the same, no movement
    if lat1 == lat2 and lng1 == lng2:
        return 0.0, 0.0

    # Convert latitude and longitude from degrees to radians
    lat1_rad = math.radians(lat1)
    lng1_rad = math.radians(lng1)
    lat2_rad = math.radians(lat2)
    lng2_rad = math.radians(lng2)

    # Calculate distance using Haversine formula
    dlat = lat2_rad - lat1_rad
    dlng = lng2_rad - lng1_rad
    a = math.sin(dlat/2)**2 + math.cos(lat1_rad) * math.cos(lat2_rad) * math.sin(dlng/2)**2
    c = 2 * math.asin(math.sqrt(a))
    distance_km = 6371 * c  # Earth's radius in kilometers

    # Calculate speed in km/h
    if time_interval_seconds > 0:
        speed_kmh = (distance_km / time_interval_seconds) * 3600
    else:
        speed_kmh = 0.0

    # Calculate direction (bearing) in degrees
    y = math.sin(lng2_rad - lng1_rad) * math.cos(lat2_rad)
    x = math.cos(lat1_rad) * math.sin(lat2_rad) - math.sin(lat1_rad) * math.cos(lat2_rad) * math.cos(lng2_rad - lng1_rad)
    bearing_rad = math.atan2(y, x)
    bearing_deg = (math.degrees(bearing_rad) + 360) % 360  # Convert to 0-360 degrees

    return speed_kmh, bearing_deg


class Device(models.Model):
    # Device model fields
    # Device model fields
    name = models.CharField(max_length=255, help_text="Name of the device")
    desc = models.TextField(
        blank=True,
        help_text="Description of the device",
    )
    euid = models.CharField(
        max_length=16,
        help_text="EUI-64 address of the device",
        validators=[eui_validator],
    )
    stat = models.CharField(
        default="Offline",
        max_length=255,
        help_text="Status of the device",
    )
    temp = models.IntegerField(
        default=0,
        help_text="Temperature of the device",
    )
    batt = models.IntegerField(
        default=0,
        help_text="Battery level of the device",
    )
    chrg = models.BooleanField(
        default=False,
        help_text="Whether the device is charging or not",
    )
    actv = models.BooleanField(
        default=False,
        help_text="Whether the device is active or not",
    )
    mntc = models.BooleanField(
        default=False,
        help_text="Whether the device is in maintenance mode or not",
    )
    hidn = models.BooleanField(
        default=False,
        help_text="Whether the device is hidden or not",
    )
    attr = models.JSONField(
        default=dict,
        help_text="JSON object representing the device's attributes_templates",
    )
    type = models.CharField(max_length=255, help_text="Type of the device")
    aset = models.CharField(
        max_length=20,
        default="Other",
        help_text="Type of asset the device is installed on",
    )
    loca = models.JSONField(
        default=get_location_template,
        help_text="JSON object representing the device's location",
    )
    speed = models.FloatField(
        default=0.0,
        help_text="Average speed of the device in km/h",
    )
    direction = models.FloatField(
        default=0.0,
        help_text="Direction of movement in degrees (0-360)",
    )
    fild = models.ForeignKey(
        Field,
        on_delete=models.CASCADE,
        related_name="devices",
        help_text="The field which this device is assigned to",
        default=None,
        blank=False,
        null=False,
    )
    offp = models.IntegerField(
        default=1,
        validators=[MinValueValidator(1)],
        help_text="Amount of time to wait for since last update for the device to be considered offline",
    )
    crat = models.DateTimeField(
        auto_now_add=True,
        help_text="Time the device was created",
    )
    lupd = models.DateTimeField(
        auto_now_add=True, help_text="Last time the device was updated"
    )

    # Device model critical fields list
    # if these fields changed, the model should be save to db immediately
    # Note: speed and direction are calculated automatically and not included here
    critical_fields = [
            "name", "desc", "euid", "temp", "batt", "chrg", "actv",
            "mntc", "hidn", "aset", "loca", "type", "attr", "offp", "fild_id",
            "crat", "lupd"
        ]

    # Device model properties
    @property
    def lupd_str(self):
        if self.lupd:
            now = timezone.now()
            last_update = self.lupd
            return f"{timesince(last_update, now)}".split(",")[0]
        return "—"

    def __str__(self):
        return self.name

    def to_dict(self):
        return {
            "id": self.id,
            "name": self.name,
            "desc": self.desc,
            "euid": self.euid,
            "stat": self.stat,
            "temp": self.temp,
            "batt": self.batt,
            "chrg": self.chrg,
            "actv": self.actv,
            "mntc": self.mntc,
            "hidn": self.hidn,
            "aset": self.aset,
            "loca": self.loca,
            "speed": self.speed,
            "direction": self.direction,
            "type": self.type,
            "attr": self.attr,
            "offp": self.offp,
            "fild": self.fild.to_json(),
            "crat": str(self.crat),
            "lupd": str(self.lupd),
        }

    def save(self, *args, **kwargs):
        from device_manager.utils.device_cache import DeviceCache
        device_cache = DeviceCache()
        # set device attributes_templates based on the device type
        update_db = True

        # Load cached device
        if self.pk:
            cached = device_cache.get_cached_device(self.pk)
            logger.info(f"Cached device: {cached} -> id: {self.pk} location: {self.loca['lati'], self.loca['long']}")
            if cached:
                # Check if location has changed
                current_lat = self.loca.get('lati', 0)
                current_lng = self.loca.get('long', 0)
                cached_lat = cached.loca.get('lati', 0) if cached.loca else 0
                cached_lng = cached.loca.get('long', 0) if cached.loca else 0

                if current_lat != cached_lat or current_lng != cached_lng:
                    # Calculate time difference in seconds
                    current_time = timezone.now()
                    cached_time = cached.lupd if cached.lupd else current_time
                    if isinstance(cached_time, str):
                        from django.utils.dateparse import parse_datetime
                        cached_time = parse_datetime(cached_time)

                    time_diff = (current_time - cached_time).total_seconds()

                    # Calculate speed and direction if location changed and time difference is reasonable
                    if time_diff > 0 and time_diff < 3600:  # Only calculate if time diff is between 0 and 1 hour
                        speed, direction = calculate_speed_and_direction(
                            cached_lat, cached_lng, current_lat, current_lng, time_diff
                        )
                        self.speed = speed
                        self.direction = direction
                        logger.info(f"Device {self.name} moved: Speed={speed:.1f} km/h, Direction={direction:.0f}°")
                    else:
                        # Reset speed if no recent movement or time diff is too large
                        self.speed = 0.0
                        self.direction = 0.0

                # Compare all fields except 'stat'
                changed = any(
                    getattr(self, field) != getattr(cached, field)
                    for field in self.critical_fields
                )
                if not changed:
                    update_db = False
        else:
            # For new devices, set speed and direction to 0
            self.speed = 0.0
            self.direction = 0.0

        # Cache updated device
        device_cache.cache_device(self)

        # Save to DB only if non-stat fields changed
        if update_db:
            super().save(*args, **kwargs)

    class Meta:
        verbose_name = "Device"
        verbose_name_plural = "Devices"

    def to_json(self):
        """
        Serializes the Device object to a JSON string.
        """
        return json.dumps(self.to_dict())

    @staticmethod
    def from_json(json_data):
        """
        Deserializes a JSON string to a Device object.
        """
        data = json.loads(json_data)
        device = Device(
            id=data.get("id"),
            name=data.get("name"),
            desc=data.get("desc"),
            euid=data.get("euid"),
            stat=data.get("stat"),
            temp=data.get("temp"),
            batt=data.get("batt"),
            chrg=data.get("chrg"),
            actv=data.get("actv"),
            mntc=data.get("mntc"),
            hidn=data.get("hidn"),
            aset=data.get("aset"),
            loca=data.get("loca"),
            speed=data.get("speed", 0.0),
            direction=data.get("direction", 0.0),
            type=data.get("type"),
            attr=data.get("attr"),
            offp=data.get("offp"),
            fild=Field.from_json(data.get("fild")),
            crat=data.get("crat"),
            lupd=data.get("lupd"),
        )
        return device


@receiver(post_save, sender=Device)
def send_update(sender, instance: Device, **kwargs):
    """
    Signal handler that sends device updates to WebSocket channels and processes device messages.

    Args:
        sender: The model class that sent the signal (unused but required by Django)
        instance: The Device instance that was saved
        kwargs: Additional keyword arguments (unused but required by Django)
    """
    from notification_center.utils import process_device_messages
    from .utils.device_cache import DeviceCache
    import asyncio

    # Improved WebSocket update function with proper async handling
    async def send_websocket_update_async():
        try:
            channel_layer = get_channel_layer()
            device_group_name = f"device_{instance.id}"

            # Get device data once to avoid race conditions
            cached_device=DeviceCache().get_cached_device(instance.id)
            if cached_device:
                device_data = cached_device.to_dict()
            else:
                device_data = instance.to_dict()

            # Use native async call instead of async_to_sync
            await channel_layer.group_send(
                device_group_name,
                {"type": "object_update", "data": device_data},
            )
        except Exception as e:
            logger.error(f"Error sending WebSocket update for device {instance.id}: {e}")

    # Run the async function in the background without blocking
    def run_async_task():
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(send_websocket_update_async())
        finally:
            loop.close()

    # Submit WebSocket update to executor with proper async handling
    executor.submit(run_async_task)

    # Submit background task to executor
    executor.submit(process_device_messages, instance)
