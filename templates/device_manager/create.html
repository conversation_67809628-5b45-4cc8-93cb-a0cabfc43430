{% extends "horizontal_base.html" %}
{% load static %}
{% block title %}
    Device Registration
{% endblock title %}
{% block extra_css %}
    <link href="{% static 'css/location_picker.css' %}" rel="stylesheet" type="text/css" />
{% endblock extra_css %}
{% block content %}
    <!-- Start Content-->
    <div class="container-fluid">
        <!-- start page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box">
                    <h4 class="page-title">
                        <a href="#" onclick="javascript:history.back()" class="link-secondary"><i class="dripicons-arrow-thin-left"></i></a> Device Registration
                    </h4>
                </div>
            </div>
        </div>
        <!-- end page title -->
        <!-- Form Start-->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        {% if form.errors %}
                            <div class="alert alert-danger">
                                <strong>Form Error!</strong> Please fix the following:
                                <ul class="mb-0">
                                    {% for field in form %}
                                        {% if field.errors %}<li>{{ field.label }}: {{ field.errors|striptags }}</li>{% endif %}
                                    {% endfor %}
                                </ul>
                            </div>
                        {% endif %}
                        <h4 class="header-title">Device Registration</h4>
                        <p class="text-muted font-14">
                            The parameter
                            <code>EUI-64</code> is device specific and can be
                            found on the device
                            label.
                        </p>
                        <ul class="nav nav-tabs nav-bordered mb-2">
                        </ul>
                        <!-- end nav-->
                        <form action="" method="post" autocomplete="off">
                            {% csrf_token %}
                            <div class="row">
                                <div class="col-lg-6">
                                    <div class="mb-2">
                                        <label for="{{ form.name.id_for_label }}" class="form-label">{{ form.name.label }}</label>
                                        {{ form.name }}
                                    </div>
                                    <div class="mb-2">
                                        <label for="{{ form.desc.id_for_label }}" class="form-label">{{ form.desc.label }}</label>
                                        {{ form.desc }}
                                    </div>
                                    <div class="row">
                                        <div class="col-lg-6">
                                            <div class="mb-2">
                                                <label for="{{ form.aset.id_for_label }}" class="form-label">{{ form.aset.label }}</label>
                                                {{ form.aset }}
                                            </div>
                                        </div>
                                        <div class="col-lg-6">
                                            <div class="mb-2">
                                                <label for="{{ form.type.id_for_label }}" class="form-label">{{ form.type.label }}</label>
                                                {{ form.type }}
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mb-2">
                                        <label for="{{ form.euid.id_for_label }}" class="form-label">{{ form.euid.label }}</label>
                                        <div class="input-group input-group-merge">
                                            {{ form.euid }}
                                            <div class="input-group-text">
                                                <span id="generate-eui" class="uil-sync"></span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mb-2">
                                        <label for="{{ form.offp.id_for_label }}" class="form-label">{{ form.offp.label }}</label>
                                        <div class="input-group input-group-merge">
                                            {{ form.offp }}
                                            <span class="input-group-text"><i class="uil-clock"></i></span>
                                        </div>
                                    </div>
                                </div>
                                <!-- end col -->
                                <div class="col-lg-6">
                                    <div class="mb-2">
                                        <div class="row">
                                            <div class="col-lg-6">
                                                <div class="mb-2">
                                                    <label for="{{ form.fild.id_for_label }}" class="form-label">{{ form.fild.label }}</label>
                                                    {{ form.fild }}
                                                </div>
                                            </div>
                                            <div class="col-lg-6">
                                                <div class="mb-2">
                                                    <label for="{{ form.plac.id_for_label }}" class="form-label">{{ form.plac.label }}</label>
                                                    {{ form.plac }}
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-lg-5">
                                                <div class="mb-2">
                                                    <label for="{{ form.lati.id_for_label }}" class="form-label">{{ form.lati.label }}</label>
                                                    {{ form.lati }}
                                                </div>
                                            </div>
                                            <div class="col-lg-5">
                                                <div class="mb-2">
                                                    <label for="{{ form.long.id_for_label }}" class="form-label">{{ form.long.label }}</label>
                                                    {{ form.long }}
                                                </div>
                                            </div>
                                            <div class="col-lg-2">
                                                <div class="mb-2">
                                                    <label for="{{ form.alti.id_for_label }}" class="form-label">{{ form.alti.label }}</label>
                                                    {{ form.alti }}
                                                </div>
                                            </div>
                                        </div>
                                        <label class="form-label">Map</label>
                                        <div class="mb-2">
                                            <input id="location-search-input" class="form-control" type="text" placeholder="Search for a location..." style="width: 100%; margin-bottom: 10px;">
                                        </div>
                                        <div id="location_picker" class="gmaps"></div>
                                    </div>
                                </div>
                            </div>
                            <button type="submit" class="btn btn-primary float-end mt-2">Save</button>
                        </form>
                    </div>
                    <!-- end row-->
                </div>
                <!-- end preview-->
            </div>
            <!-- end card-body -->
        </div>
        <!-- end card -->
    </div>
    <!-- end col -->
</div>
<!-- end row -->
</div>
<!-- container -->
{% endblock content %}
{% block extra_javascript %}
    <!-- Third party JS -->
    <script src="{% static 'js/custom/location_picker.js' %}"></script>
    <script src="https://maps.googleapis.com/maps/api/js?key={{ GOOGLE_MAPS_API_KEY }}&libraries=places&callback=initMap"
            defer></script>
    <!-- App js -->
    <script src="{% static 'js/custom/generators.js' %}"></script>
{% endblock extra_javascript %}
