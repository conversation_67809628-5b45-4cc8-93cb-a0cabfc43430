{% extends "horizontal_base.html" %}
{% load static %}
{% block title %}
  Dashboard
{% endblock title %}
{% block extra_css %}
<style>

</style>
{% endblock extra_css %}
{% block content %}
  <!-- Start Content-->
  <div id="network-overview" class="container-fluid">
    <!-- start page title -->
    <div class="row">
      <div class="col-lg">
        <div class="page-title-box">
          <div class="col-lg">
            <div class="page-title-box d-flex align-items-center">
              <h4 class="page-title">Field</h4>
              <div class="form-group mb-0 ms-3">
                <div class="input-group">
                  <select class="form-select" id="field-selection">
                    <option value="0" {% if selected_field_id == "0" %}selected{% endif %}>All</option>
                    {% for field in list %}<option value="{{ field.id }}">{{ field.name }}</option>{% endfor %}
                  </select>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- end page title -->
    <!-- Recent Acitivty, Asset List, & Network Map -->
    <div class="row">
      <!-- Recent Acitivty & Asset List -->
      <div class="col-xl-3">
        <!-- Asset List -->
        <div class="card" style="height: 35vh;">
          <div class="card-body border-top-0 border-start-0 border-end-0 border-dashed border"
               style="max-height: 0px">
            <h3 class="header-title">Assets</h3>
          </div>
          <div class="card-body pt-0" data-simplebar style="height: 28vh">
            <div id="asset-list-container"></div>
          </div>
        </div>
        <!-- Recent Activity -->
        <div class="card" style="height: 35vh;">
          <div class="card-body" style="max-height: 0px">
            <h3 class="header-title">
              Recent Activity <span id="ws-connection" class="badge">Disconnected</span>
            </h3>
          </div>
          <div class="card-header bg-light-lighten border-top border-bottom border-light py-1 text-center">
            <div class="d-flex align-items-center justify-content-center">
              <i class="mdi mdi-access-point mdi-18px px-1"></i>
              <p class="m-0">
                Last update <b id="last-update">—</b> ago
              </p>
            </div>
          </div>
          <div class="card-body pt-2" data-simplebar style="height: 22vh">
            <div class="timeline-alt py-0">
              <div id="recent-activity-container"></div>
            </div>
          </div>
        </div>
      </div>
      <!-- Network Map -->
      <div class="col-xl-9">
        <div class="card">
          <div class="card-body">
            <div class="d-flex justify-content-between align-items-center mb-2">
              <h3 class="header-title">Map</h3>
              <div class="d-flex align-items-center">
              </div>
            </div>
            <div id="device_map" class="gmaps" style="height: 64.5vh"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- Floating Controls Panel -->
    <div id="map-controls-panel" class="position-fixed">
      <div class="card mb-0">
        <div class="card-header bg-primary text-white">
          <h5 class="card-title mb-0">
            <i class="mdi mdi-cog me-2"></i>Map Controls
          </h5>
        </div>
        <div class="card-body">
          <!-- Cluster Radius Control -->
          <div class="mb-3">
            <label for="cluster-radius" class="form-label">
              <i class="mdi mdi-group me-1"></i>Cluster Radius
            </label>
            <div class="input-group">
              <input type="range" class="form-control" id="cluster-radius" value="3" min="0.1" max="10" step="0.1">
              <span class="input-group-text"><span id="radius-value-display">3</span> km</span>
            </div>
          </div>

          <!-- Zoom Level Control -->
          <div class="mb-3">
            <label class="form-label">
              <i class="mdi mdi-magnify me-1"></i>Zoom Level
            </label>
            <div class="input-group mb-2">
              <span class="input-group-text">Current:</span>
              <span class="input-group-text"><span id="current-zoom-display">8</span></span>
            </div>
            <div class="input-group">
              <span class="input-group-text">Show Markers at Zoom ≥</span>
              <select class="form-select" id="marker-visibility-zoom">
                <option value="10">10</option>
                <option value="11">11</option>
                <option value="12">12</option>
                <option value="13" selected>13</option>
                <option value="14">14</option>
                <option value="15">15</option>
              </select>
            </div>
          </div>

          <!-- Additional Controls -->
          <div class="mb-0">
            <div class="form-check form-switch mb-2">
              <input class="form-check-input" type="checkbox" id="auto-zoom-toggle-panel">
              <label class="form-check-label" for="auto-zoom-toggle-panel">
                <i class="mdi mdi-crosshairs-gps me-1"></i>Auto-zoom on updates
              </label>
            </div>
            {% if DEBUG %}
            <div class="form-check form-switch">
              <input class="form-check-input" type="checkbox" id="debug-mode-toggle-panel" checked>
              <label class="form-check-label" for="debug-mode-toggle-panel">
                <i class="mdi mdi-bug me-1"></i>Debug mode
              </label>
            </div>
            {% endif %}
          </div>
        </div>
      </div>
    </div>

    <!-- Toggle Button -->
    <button id="controls-toggle-btn" class="btn btn-primary position-fixed" title="Toggle Map Controls">
      <i class="mdi mdi-cog mdi-18px center"></i>
    </button>

  </div>
{% endblock content %}
{% block extra_javascript %}
  <!-- Third party js -->
  <script src="https://unpkg.com/@googlemaps/markerclusterer@2.0.15/dist/index.min.js"></script>
  <script src="{% static 'js/custom/dashboards/common/device_map.js' %}"></script>
  <script src="https://maps.googleapis.com/maps/api/js?key={{ GOOGLE_MAPS_API_KEY }}&libraries=geometry&callback=initMap"
          defer></script>
  <!-- App js -->
  <script>
    // load field ids
    const fieldIds = {{ selected_field_id }} ? [{{ selected_field_id }}] : {{ field_ids }};
    const selectedFieldId = {{ selected_field_id }};
    // default cluster radius in kilometers
    const defaultClusterRadius = 3;
  </script>
  <script src="{% static 'js/custom/select_field.js' %}"></script>
  <script src="{% static 'js/custom/dashboards/common/events_list_generator.js' %}"></script>
  <script src="{% static 'js/custom/dashboards/common/time_elapsed.js' %}"></script>
  <script src="{% static 'js/custom/dashboards/overview.js' %}"></script>
  <script src="{% static 'js/custom/dashboards/common/fetch_fields.js' %}"></script>
{% endblock extra_javascript %}
