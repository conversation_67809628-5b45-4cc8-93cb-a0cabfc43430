<div class="device-selection-widget" data-field-name="{{ name }}">
    <div class="row mb-3">
        <div class="col-md-4">
            <label for="device-asset-filter" class="form-label">Asset:</label>
            <select id="device-asset-filter" class="form-select">
                <option value="">All Assets</option>
                {% for aset in device_asets %}
                    <option value="{{ aset }}">{{ aset }}</option>
                {% endfor %}
            </select>
        </div>
        <div class="col-md-4">
            <label for="device-name-filter" class="form-label">Device Name:</label>
            <input type="text" id="device-name-filter" class="form-control" placeholder="Search device name...">
        </div>
        <div class="col-md-4">
            <label class="form-label">&nbsp;</label>
            <div>
                <button type="button" class="btn btn-outline-secondary btn-sm" id="clear-all-filters">
                    Clear All Filters
                </button>
            </div>
        </div>
    </div>

    <div class="row mb-3">
        <div class="col-12">
            <label class="form-label">Fields:</label>
            <div class="field-filter-buttons">
                {% for field in fields %}
                    <button type="button" class="btn btn-outline-primary btn-sm me-2 mb-2 field-filter-btn"
                            data-field-id="{{ field.id }}">{{ field.name }}</button>
                {% endfor %}
                <button type="button" class="btn btn-outline-secondary btn-sm me-2 mb-2"
                        id="clear-field-filter">All Fields</button>
            </div>
        </div>
    </div>

    <div class="table-responsive" id="device-table-container">
        <table class="table table-bordered table-hover" id="device-selection-table">
            <thead class="table-light">
                <tr>
                    <th>Field</th>
                    <th>Device</th>
                    <th>Asset</th>
                    <th>Select</th>
                </tr>
            </thead>
            <tbody>
                {% for device in devices %}
                    <tr data-device-id="{{ device.id }}" data-field-id="{{ device.fild.id }}" data-asset="{{ device.aset }}">
                        <td>{{ device.fild.name }}</td>
                        <td>{{ device.name }}</td>
                        <td>{{ device.aset }}</td>
                        <td>
                            <input type="checkbox" name="{{ name }}" value="{{ device.id }}"
                                   class="form-check-input device-checkbox"
                                   {% if device.id|stringformat:"s" in selected_ids %}checked{% endif %}>
                        </td>
                    </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <div class="row mt-3">
        <div class="col-12">
            <button type="button" class="btn btn-sm btn-outline-primary me-2" id="select-all-devices">
                Select All
            </button>
            <button type="button" class="btn btn-sm btn-outline-secondary" id="deselect-all-devices">
                Deselect All
            </button>
        </div>
    </div>
</div>
