{% extends "horizontal_base.html" %}
{% load static %}
{% block title %}
    Create New Field
{% endblock title %}
{% block extra_css %}
    <link href="{% static 'css/location_picker.css' %}" rel="stylesheet" type="text/css" />
{% endblock extra_css %}
{% block content %}
    <!-- Start Content-->
    <div class="container-fluid">
        <!-- start page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box">
                    <h4 class="page-title">
                        <a href="#" onclick="javascript:history.back()" class="link-secondary"><i class="dripicons-arrow-thin-left"></i></a> Field Creation
                    </h4>
                </div>
            </div>
        </div>
        <!-- end page title -->
        <!-- Form Start-->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        {% if form.errors %}
                            <div class="alert alert-danger">
                                <strong>Form Error!</strong> Please fix the following:
                                <ul class="mb-0">
                                    {% for field in form %}
                                        {% if field.errors %}<li>{{ field.label }}: {{ field.errors|striptags }}</li>{% endif %}
                                    {% endfor %}
                                </ul>
                            </div>
                        {% endif %}
                        <h4 class="header-title">Polygon Mapping</h4>
                        <p class="text-muted font-14">Create fields by drawing their borders using polygons on the map.</p>
                        <ul class="nav nav-tabs nav-bordered mb-2">
                        </ul>
                        <!-- end nav-->
                        <form action="" method="post" autocomplete="off">
                            {% csrf_token %}
                            <div class="row">
                                <div class="col-lg-6">
                                    <div class="mb-2">
                                        <label for="{{ form.name.id_for_label }}" class="form-label">{{ form.name.label }}</label>
                                        {{ form.name }}
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="mb-2">
                                        <label for="{{ form.colr.id_for_label }}" class="form-label">{{ form.colr.label }}</label>
                                        {{ form.colr }}
                                    </div>
                                    {{ form.cord }}
                                    {{ form.covr }}
                                    {{ form.loca }}
                                </div>
                            </div>
                            <div class="row gy-2 gx-2 justify-content-between align-items-center">
                                <div class="col-lg-2">
                                    <label for="work-shifts-container" class="form-label"> Work Shifts</label>
                                </div>
                                <div class="col-lg-2 d-flex justify-content-end align-items-center">
                                    <button class="form-control me-2 apply-to-all" type="button">Apply All</button>
                                    <i class="mdi mdi-alarm-plus me-2 add-shift" style="font-size: 20px;"></i>
                                </div>
                            </div>
                            
                            <div id="work-shifts-container" class="mb-2">
                                {% if work_shifts %}
                                    {% for shift in work_shifts %}
                                        <div class="work-shift row gy-2 gx-2 align-items-center">
                                            <div class="col-lg">
                                                <select name="day" class="form-select" >
                                                    {% for day in DAYS_OF_WEEK %}
                                                    <option value="{{ day }}" {% if shift.day == day %} selected {% endif %}>
                                                        {{ day|title }}
                                                    </option>
                                                    {% endfor %}                                        
                                                </select>
                                            </div>
                                            <div class="col-lg">
                                                <input type="time" class="form-control" name="start_time" value="{{ shift.start_time }}" required>
                                            </div>
                                            <div class="col-auto mt-3">
                                                <p> - </p>
                                                </div>            
                                            <div class="col-lg">
                                                <input type="time" class="form-control" name="end_time" value="{{ shift.end_time }}" required>
                                            </div>
                                            <div class="col-auto">
                                                <i class="mdi mdi-delete me-2 remove-shift" style="font-size: 20px;"></i>
                                            </div>
                                        </div>
                                    {% endfor %}
                                {% endif %}
                            </div>
                            <label class="form-label">Map</label>
                            <div class="mb-2">
                                <input id="field-search-input" class="form-control" type="text" placeholder="Search for a location..." style="width: 100%; margin-bottom: 10px;">
                            </div>
                            <div id="polygon_drawer" style="height: 500px;" class="gmaps"></div>
                            <button type="submit" class="btn btn-primary float-end mt-2">Save</button>
                        </form>
                    </div>
                    <!-- end row-->
                </div>
                <!-- end preview-->
            </div>
            <!-- end card-body -->
        </div>
        <!-- end card -->
    </div>
    <!-- end col -->
</div>
<!-- end row -->
</div>
<!-- container -->
{% endblock content %}
{% block extra_javascript %}
    <script> const daysOfWeek = {{ DAYS_OF_WEEK|safe }};</script>
    <script src="{% static 'js/custom/polygon_mapping.js' %}"></script>
    <script src="https://maps.googleapis.com/maps/api/js?key={{ GOOGLE_MAPS_API_KEY }}&language=en&libraries=drawing,places&callback=initMap&loading=async"
            async
            defer></script>
    <script src="{% static 'js/custom/work_shifts.js' %}"></script>
{% endblock extra_javascript %}
