{% extends "horizontal_base.html" %}
{% load static %}
{% block title %}
    Downlink | Packet Analyzer
{% endblock title %}
{% block content %}
    <!-- Start Content-->
    <div class="container-fluid">
        <!-- start page title -->
        <div class="row mt-1">
            <div class="col-lg">
                <div class="page-title-box">
                    <h4 class="page-title">Downlink Packets</h4>
                </div>
            </div>
            <div class="col-lg-auto">  
                <div class="modal-footer d-flex justify-content-center align-items-center px-0 mt-1">
                    <form action="{% url 'packet_analyzer:export_csv' %}" method="get">
                        <input type="hidden" name="packet_status" value="downlink">
                        <button type="submit" class="btn rounded-pill mb-3 btn-secondary"><i class="mdi mdi-file-download-outline">Download CSV</i></button>
                    </form>   
                </div>
            </div>
        </div>
        <!-- end page title -->
        <!-- end row-->
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table id="downlinkTable"
                           class="table table-centered table-nowrap order-column mb-0 hover">
                        <thead class="table-light">
                            <tr>
                                <th>Created At</th>
                                <th>Transmitted</th>
                                <th>Device</th>
                                <th>Gateway</th>
                                <th>Packet Count</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for downlink_packet in packet_list %}
                                <tr>
                                    <td>{{ downlink_packet.crat }}</td>
                                    {% if downlink_packet.txat != None %}
                                        <td>{{ downlink_packet.txat }}</td>
                                    {% else %}
                                        <td>Queued</td>
                                    {% endif %}
                                    <td>
                                        <a href="{% url 'device_manager:detail' downlink_packet.devi.id %}"
                                           class="text-title"><b>{{ downlink_packet.devi.name }}</b></a>
                                    </td>
                                    <td>
                                        <a href="{% url 'device_manager:detail' downlink_packet.gate.id %}"
                                           class="text-title"><b>{{ downlink_packet.gate.name }}</b></a>
                                    </td>
                                    <td id="centered-td-number">{{ downlink_packet.cont }}</td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                    <!-- end row-->
                     <!-- Pagination Controls -->
                    <div class="d-flex justify-content-between align-items-center mt-3">
                        <div>
                            <p class="mb-0">
                                Showing page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                            </p>
                        </div>
                        <nav>
                            <ul class="pagination pagination-rounded mb-0">
                                {% if page_obj.has_previous %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}">&laquo; Prev</a>
                                    </li>
                                {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link">&laquo; Prev</span>
                                    </li>
                                {% endif %}

                                {% for num in page_obj.paginator.page_range %}
                                    {% if page_obj.number == num %}
                                        <li class="page-item active"><span class="page-link">{{ num }}</span></li>
                                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                        <li class="page-item"><a class="page-link" href="?page={{ num }}">{{ num }}</a></li>
                                    {% endif %}
                                {% endfor %}

                                {% if page_obj.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.next_page_number }}">Next &raquo;</a>
                                    </li>
                                {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link">Next &raquo;</span>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                    </div>
                    <!-- end row-->
                </div>
            </div>
        </div>
    </div>
    <!-- container -->
{% endblock content %}
{% block extra_javascript %}
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>
    <!-- Datatables -->
    <script src="{% static 'js/vendor.min.js' %}"></script>
    <!-- Third party js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.12.9/umd/popper.min.js"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/js/bootstrap.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.6.1/font/bootstrap-icons.css"
          rel="stylesheet" />
    <!-- Third party js ends -->
    <!-- Init js -->
    <!-- Init js end -->
{% endblock extra_javascript %}