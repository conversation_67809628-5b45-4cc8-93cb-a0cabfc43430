{% extends "horizontal_base.html" %}
{% load static %}
{% block title %}
    Dropped | Packet Analyzer
{% endblock title %}
{% block content %}
    <!-- Start Content-->
    <div class="container-fluid">
        <!-- start page title -->
        <div class="row mt-1">
            <div class="col-lg">
                <div class="page-title-box">
                    <h4 class="page-title">Dropped Packets</h4>
                </div>
            </div>
            <div class="col-lg-auto">  
                <div class="modal-footer d-flex justify-content-center align-items-center px-0 mt-1">
                    <form action="{% url 'packet_analyzer:export_csv' %}" method="get">
                        <input type="hidden" name="packet_status" value="dropped">
                        <button type="submit" class="btn rounded-pill mb-3 btn-secondary"><i class="mdi mdi-file-download-outline">Download CSV</i></button>
                    </form>   
                </div>
            </div>
        </div>
        <!-- end page title -->
        <!-- end row-->
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table id="droppedTable"
                           class="table table-centered table-nowrap order-column mb-0 hover">
                        <thead class="table-light">
                            <tr>
                                <th>ID</th>
                                <th>Device</th>
                                <th>Gateway</th>
                                <th>Received</th>
                                <th>Data</th>
                                <th>Exception</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for dropped_packet in packet_list %}
                                <tr>
                                    <td>{{ dropped_packet.id }}</td>
                                    <td>{% if dropped_packet.devi and dropped_packet.devi.name %}{{ dropped_packet.devi.name }}{% else %}-{% endif %}</td>
                                    <td>{% if dropped_packet.gate and dropped_packet.gate.name %}{{ dropped_packet.gate.name }}{% else %}-{% endif %}</td>
                                    <td>{{ dropped_packet.rxat }}</td>
                                    <td>
                                        <button type="button" tabindex="0" class="btn btn-primary btn-sm" data-bs-toggle="popover" data-bs-trigger="focus" data-bs-content="{{ dropped_packet.data }}" title="Data">
                                            View
                                        </button>
                                    <td>
                                        <button type="button" tabindex="0" class="btn btn-danger btn-sm" data-bs-toggle="popover" data-bs-trigger="focus" data-bs-content="{{ dropped_packet.expt }}" title="Exception">
                                            View
                                        </button>                                        
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                    <!-- end row-->
                     <!-- Pagination Controls -->
                    <div class="d-flex justify-content-between align-items-center mt-3">
                        <div>
                            <p class="mb-0">
                                Showing page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                            </p>
                        </div>
                        <nav>
                            <ul class="pagination pagination-rounded mb-0">
                                {% if page_obj.has_previous %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}">&laquo; Prev</a>
                                    </li>
                                {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link">&laquo; Prev</span>
                                    </li>
                                {% endif %}

                                {% for num in page_obj.paginator.page_range %}
                                    {% if page_obj.number == num %}
                                        <li class="page-item active"><span class="page-link">{{ num }}</span></li>
                                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                        <li class="page-item"><a class="page-link" href="?page={{ num }}">{{ num }}</a></li>
                                    {% endif %}
                                {% endfor %}

                                {% if page_obj.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.next_page_number }}">Next &raquo;</a>
                                    </li>
                                {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link">Next &raquo;</span>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                    </div>
                    <!-- end row-->
                </div>
            </div>
        </div>
    </div>
    <!-- container -->
{% endblock content %}
{% block extra_javascript %}
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>
    <!-- Datatables -->
    <script src="{% static 'js/vendor.min.js' %}"></script>
    <!-- Third party js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.12.9/umd/popper.min.js"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/js/bootstrap.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.6.1/font/bootstrap-icons.css"
          rel="stylesheet" />
    <!-- Third party js ends -->
    <!-- Init js -->
    <!-- Init js end -->
{% endblock extra_javascript %}