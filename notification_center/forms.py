from django import forms
from django.template.loader import render_to_string
from django.contrib.auth.models import User
from device_manager.models import Device
from fields.models import Field
from accounts.models import UserProfile
from .models import NotificationSettings


class DeviceSelectionWidget(forms.Widget):
    def __init__(self, attrs=None, user=None, show_all_devices=False):
        super().__init__(attrs)
        self.user = user
        self.show_all_devices = show_all_devices

    def render(self, name, value, attrs=None, renderer=None):
        if attrs is None:
            attrs = {}

        if self.show_all_devices or (self.user and self.user.is_superuser):
            devices = Device.objects.all().select_related('fild')
        elif self.user:
            try:
                user_profile = UserProfile.objects.get(user=self.user)
                devices = user_profile.devs.all().select_related('fild')
            except UserProfile.DoesNotExist:
                devices = Device.objects.none()
        else:
            devices = Device.objects.none()

        fields = Field.objects.filter(devices__in=devices).distinct()
        device_asets = devices.values_list('aset', flat=True).distinct()

        selected_ids = []
        if value:
            selected_ids = [str(v) for v in value] if isinstance(value, (list, tuple)) else [str(value)]

        context = {
            'name': name,
            'devices': devices,
            'fields': fields,
            'device_asets': device_asets,
            'selected_ids': selected_ids,
        }

        return render_to_string('widgets/device_selection_widget.html', context)

    def value_from_datadict(self, data, files, name):
        return data.getlist(name)


class NotificationSettingsForm(forms.ModelForm):
    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)

        # Use custom widget for device selection
        self.fields['devs'].widget = DeviceSelectionWidget(user=self.user)

    class Meta:
        model = NotificationSettings
        fields = ["rxif", "rxup", "rxwr", "rxdg", "devs", "mthd"]
        labels = {
            "rxif": "Info",
            "rxup": "Updates",
            "rxwr": "Warnings",
            "rxdg": "Danger",
            "devs": "Devices",
            "mthd": "Delivery Method",
        }
        widgets = {
            "rxif": forms.CheckboxInput(
                attrs={"type": "checkbox", "data-switch": "info"}
            ),
            "rxup": forms.CheckboxInput(
                attrs={"type": "checkbox", "data-switch": "primary"}
            ),
            "rxwr": forms.CheckboxInput(
                attrs={"type": "checkbox", "data-switch": "warning"}
            ),
            "rxdg": forms.CheckboxInput(
                attrs={"type": "checkbox", "data-switch": "danger"}
            ),
            "mthd": forms.Select(
                attrs={
                    "class": "form-control",
                    "data-toggle": "form-select",
                }
            ),
        }
