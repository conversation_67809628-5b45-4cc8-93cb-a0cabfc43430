import csv
from .models import Uplink<PERSON>acket, DownlinkPacket, DroppedPacket, Device
from django.views.generic import ListView
from django.utils import timezone
from datetime import timedelta
from django.http import HttpResponse,HttpResponseForbidden
from django.contrib.auth.mixins import LoginRequiredMixin, UserPassesTestMixin
from django.contrib.auth.decorators import login_required, user_passes_test
from routes.decorators import admin_required
from routes.mixins import AdminRequiredMixin



# Create your views here.
class UplinkListView(AdminRequiredMixin, ListView):
    model = UplinkPacket
    template_name = "packet_analyzer/uplink_list.html"
    context_object_name = "packet_list"
    paginate_by = 10 # Number of items per page
    def get_queryset(self):
        # Create a queryset with defer applied directly
        cutoff_time = timezone.now() - timedelta(hours=24)

        # Create a queryset that defers 'data' and 'deco', and filters for the last 24 hours
        queryset = self.model.objects.defer("data", "deco").filter(
            rxat__gte=cutoff_time
        )  # Filter for records with rxat in the last 24 hours
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        return context


class DownlinkListView(AdminRequiredMixin, ListView):
    model = DownlinkPacket
    template_name = "packet_analyzer/downlink_list.html"
    context_object_name = "packet_list"
    paginate_by = 10 # Number of items per page
    def get_queryset(self):
        queryset = super().get_queryset()
        return queryset.order_by("-txat")

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        return context


class DroppedListView(AdminRequiredMixin, ListView):
    model = DroppedPacket
    template_name = "packet_analyzer/dropped_list.html"
    context_object_name = "packet_list"
    paginate_by = 10 # Number of items per page
    def get_queryset(self):
        queryset = super().get_queryset()
        return queryset.order_by("-rxat")

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        return context

@login_required
def export_to_csv(request):
    # Create the HttpResponse object with the appropriate CSV header.
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = 'attachment; filename="notification_export.csv"'

    writer = csv.writer(response)
    
    # Determine the devices to filter by
    if request.user.is_superuser:
        # Superusers can view all devices
        user_devices = Device.objects.all()
    else:
        # Regular users can only view their assigned devices
        user_devices = request.user.userprofile.devs.all()
    
    if 'packet_status' in request.GET:
        packet_status = request.GET['packet_status']
        if packet_status == 'uplink':
            response['Content-Disposition'] = 'attachment; filename="uplink_export.csv"'
            cutoff_time = timezone.now() - timedelta(hours=24)
            queryset = UplinkPacket.objects.defer("data", "deco").filter(
            rxat__gte=cutoff_time, devi__in=user_devices # Filter for records with rxat in the last 24 hours
        )
            writer.writerow(['Device', 'Gateway', 'Packet Count','RSSI','SNR',
                             'Channel','Frequency','Transmitted At','Received At',
                             'Data','Decoded Packet'])
            # Write data rows
            for obj in queryset:
                writer.writerow([obj.devi.name,
                                  obj.gate.name, 
                                  obj.cont,
                                  obj.rssi,
                                  obj.snr,
                                  obj.chan,
                                  obj.freq,
                                  obj.txat,
                                  obj.rxat,
                                  obj.data,
                                  obj.deco])
        elif packet_status == 'downlink':
            response['Content-Disposition'] = 'attachment; filename="downlink_export.csv"'
            queryset = DownlinkPacket.objects.filter(devi__in=user_devices).order_by("-txat")
            writer.writerow(['Gateway', 'Device', 'Packet Count','Decoded Packet','Data',
                             'Channel','Frequency','Transmit Power','Created At',
                             'Transmitted At'])
            # Write data rows
            for obj in queryset:
                writer.writerow([obj.gate.name,
                                  obj.devi.name, 
                                  obj.cont,
                                  obj.deco,
                                  obj.data,
                                  obj.chan,
                                  obj.freq,
                                  obj.txpr,
                                  obj.crat,
                                  obj.txat])
        elif packet_status == 'dropped':
            if not request.user.is_superuser:
                return HttpResponseForbidden("You do not have permission to access this resource.")
            response['Content-Disposition'] = 'attachment; filename="dropped_export.csv"'
            queryset = DroppedPacket.objects.order_by("-rxat")
            writer.writerow(['Device', 'Gateway', 'Data', 'Received At', 'Exception'])
            # Write data rows
            for obj in queryset:
                writer.writerow([
                    obj.devi.name if obj.devi and obj.devi.name else "-",
                    obj.gate.name if obj.gate and obj.gate.name else "-",
                    obj.data,
                    obj.rxat,
                    obj.expt
                ])
        else:
            # Handle invalid packet_status
            return HttpResponse(status=400)
    
    

    return response