from django.conf import settings
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib.auth.mixins import LoginRequiredMixin
from django.core.paginator import Paginator
from django.db.models import Count
from django.http import JsonResponse
from django.shortcuts import get_object_or_404, redirect, render
from django.urls import reverse_lazy
from django.utils.decorators import method_decorator
from django.views.generic import CreateView, ListView, UpdateView
from django.core.exceptions import ValidationError
from .forms import FieldForm
from .models import Field
from .utils import prepare_work_shifts_context, get_work_shifts_from_form
from routes.decorators import admin_required, authenticated_required
from routes.mixins import AdminRequiredMixin, AuthenticatedRequiredMixin

DAYS_OF_WEEK = [
    "sunday",
    "monday",
    "tuesday",
    "wednesday",
    "thursday",
    "friday",
    "saturday",
]


def get(request, field_id):
    field = get_object_or_404(Field, id=field_id)
    return JsonResponse(field.to_dict())


@admin_required
def delete(request, field_id):
    field = get_object_or_404(Field, id=field_id)
    field.delete()
    return redirect("fields:list")



class FieldListView(AuthenticatedRequiredMixin, ListView):
    model = Field
    template_name = "fields/list.html"
    context_object_name = "list"

    def get_queryset(self):
        queryset = super().get_queryset()
        # All authenticated users can view fields
        return queryset.order_by("name")

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        queryset = context["list"]
        annotated_queryset = queryset.annotate(num_devices=Count("devices"))
        fields_with_counts = {
            field.id: field.num_devices for field in annotated_queryset
        }
        context["no_devi"] = fields_with_counts
        return context


class FieldCreateView(AdminRequiredMixin, CreateView):
    model = Field
    form_class = FieldForm
    template_name = "fields/create.html"
    success_url = reverse_lazy("fields:list")

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["GOOGLE_MAPS_API_KEY"] = settings.GOOGLE_MAPS_API_KEY
        context["DAYS_OF_WEEK"] = DAYS_OF_WEEK
        return context

    def form_valid(self, form):
        try:
            field = form.save(commit=False)
            field.work_shifts = get_work_shifts_from_form(self.request)
            field.save()
            return super().form_valid(form)
        except ValidationError as e:
            form.add_error("work_shifts", e.message)
            return self.form_invalid(form)

class FieldUpdateView(AdminRequiredMixin, UpdateView):
    model = Field
    form_class = FieldForm
    template_name = "fields/create.html"
    pk_url_kwarg = "field_id"
    context_object_name = "field"
    success_url = reverse_lazy("fields:list")
    next = None

    def dispatch(self, request, *args, **kwargs):
        try:
            self.next = request.META["QUERY_STRING"].split("=")[1]
        except:
            pass

        return super().dispatch(request, *args, **kwargs)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["GOOGLE_MAPS_API_KEY"] = settings.GOOGLE_MAPS_API_KEY
        context["DAYS_OF_WEEK"] = DAYS_OF_WEEK
        context["work_shifts"] = prepare_work_shifts_context(self.get_object())
        return context

    def form_valid(self, form):
        try:
            field = form.save(commit=False)
            field.work_shifts = get_work_shifts_from_form(self.request)
            field.save()
            return super().form_valid(form)
        except ValidationError as e:
            form.add_error("work_shifts", e.message)
            return self.form_invalid(form)
