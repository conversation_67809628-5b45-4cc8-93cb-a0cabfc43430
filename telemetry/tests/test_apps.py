"""
Unit tests for telemetry apps configuration

Use --settings=app.test_settings for running tests

example:
python manage.py test telemetry.tests.test_apps --settings=app.test_settings
"""

from django.test import TestCase, tag
from django.apps import apps
from django.conf import settings

from telemetry.apps import TelemetryConfig


@tag('unit')
class TelemetryAppsUnitTests(TestCase):
    """Unit tests for TelemetryConfig"""
    
    def test_telemetry_config_class_attributes(self):
        """Test TelemetryConfig class has correct attributes"""
        self.assertEqual(TelemetryConfig.default_auto_field, "django.db.models.BigAutoField")
        self.assertEqual(TelemetryConfig.name, "telemetry")
        
    def test_telemetry_config_inheritance(self):
        """Test TelemetryConfig inherits from AppConfig"""
        from django.apps import AppConfig
        self.assertTrue(issubclass(TelemetryConfig, AppConfig))


@tag('integration')
class TelemetryAppsIntegrationTests(TestCase):
    """Integration tests for telemetry app configuration"""
    
    def test_telemetry_app_is_installed(self):
        """Test that telemetry app is properly installed"""
        self.assertIn('telemetry.apps.TelemetryConfig', settings.INSTALLED_APPS)
        
    def test_telemetry_app_is_registered(self):
        """Test that telemetry app is registered with Django"""
        app_config = apps.get_app_config('telemetry')
        self.assertIsInstance(app_config, TelemetryConfig)
        
    def test_telemetry_app_models_are_loaded(self):
        """Test that telemetry app models are properly loaded"""
        app_config = apps.get_app_config('telemetry')
        models = app_config.get_models()
        
        # Should have at least the Telemetry model
        model_names = [model.__name__ for model in models]
        self.assertIn('Telemetry', model_names)
        
    def test_telemetry_app_ready_method(self):
        """Test that app ready method works (even if not overridden)"""
        app_config = apps.get_app_config('telemetry')
        
        # Should not raise any errors when calling ready
        try:
            app_config.ready()
        except Exception as e:
            self.fail(f"App ready method failed: {e}")
            
    def test_telemetry_app_verbose_name(self):
        """Test telemetry app verbose name"""
        app_config = apps.get_app_config('telemetry')
        
        # Default verbose name should be the app name capitalized
        expected_verbose_name = "Telemetry"
        self.assertEqual(app_config.verbose_name, expected_verbose_name)
        
    def test_telemetry_app_path(self):
        """Test telemetry app path is correct"""
        app_config = apps.get_app_config('telemetry')
        
        # Path should end with 'telemetry'
        self.assertTrue(app_config.path.endswith('telemetry'))
        
    def test_telemetry_app_module(self):
        """Test telemetry app module is correct"""
        app_config = apps.get_app_config('telemetry')
        
        # Module should be the telemetry package
        import telemetry
        self.assertEqual(app_config.module, telemetry)
        
    def test_telemetry_app_models_module(self):
        """Test telemetry app models module is accessible"""
        app_config = apps.get_app_config('telemetry')
        
        # Should be able to access models module
        models_module = app_config.models_module
        self.assertIsNotNone(models_module)
        
        # Should have Telemetry model
        self.assertTrue(hasattr(models_module, 'Telemetry'))


@tag('e2e')
class TelemetryAppsE2ETests(TestCase):
    """End-to-end tests for telemetry app configuration"""
    
    def test_complete_app_loading_cycle(self):
        """Test complete app loading and configuration cycle"""
        # Test that we can get the app config
        app_config = apps.get_app_config('telemetry')
        self.assertIsInstance(app_config, TelemetryConfig)
        
        # Test that we can get models from the app
        models = app_config.get_models()
        self.assertTrue(len(list(models)) > 0)
        
        # Test that we can import and use the models
        from telemetry.models import Telemetry
        self.assertTrue(hasattr(Telemetry, '_meta'))
        
        # Test that the model is properly configured
        self.assertEqual(Telemetry._meta.app_label, 'telemetry')
        
    def test_app_registry_consistency(self):
        """Test that app registry is consistent"""
        # Get app config through different methods
        config1 = apps.get_app_config('telemetry')
        config2 = apps.get_containing_app_config('telemetry.models')
        
        # Should be the same instance
        self.assertIs(config1, config2)
        
    def test_model_registration_through_app(self):
        """Test that models are properly registered through the app"""
        app_config = apps.get_app_config('telemetry')
        
        # Get model through app config
        telemetry_model = app_config.get_model('Telemetry')
        
        # Should be the same as direct import
        from telemetry.models import Telemetry
        self.assertIs(telemetry_model, Telemetry)
        
    def test_app_dependencies(self):
        """Test that app dependencies are properly handled"""
        app_config = apps.get_app_config('telemetry')
        
        # Telemetry app depends on device_manager and fields
        # Test that these dependencies are available
        device_manager_config = apps.get_app_config('device_manager')
        fields_config = apps.get_app_config('fields')
        
        self.assertIsNotNone(device_manager_config)
        self.assertIsNotNone(fields_config)
        
        # Test that we can access models from dependencies
        Device = device_manager_config.get_model('Device')
        Field = fields_config.get_model('Field')
        
        self.assertIsNotNone(Device)
        self.assertIsNotNone(Field)
        
    def test_app_migration_consistency(self):
        """Test that app migrations are consistent with models"""
        from django.db import connection
        from django.db.migrations.executor import MigrationExecutor
        
        executor = MigrationExecutor(connection)
        
        # Check if there are any unapplied migrations for telemetry
        plan = executor.migration_plan([('telemetry', None)])
        
        # In a properly set up test environment, there should be no unapplied migrations
        # This test ensures the app configuration is consistent with the database schema
        unapplied_migrations = [migration for migration, backwards in plan if not backwards]
        
        # If this fails, it might indicate missing migrations or configuration issues
        # In test environment, this should typically be empty
        self.assertEqual(len(unapplied_migrations), 0, 
                        f"Found unapplied migrations: {unapplied_migrations}")
                        
    def test_app_settings_integration(self):
        """Test that app integrates properly with Django settings"""
        # Test that the app is in INSTALLED_APPS
        self.assertIn('telemetry.apps.TelemetryConfig', settings.INSTALLED_APPS)
        
        # Test that we can get the app config
        app_config = apps.get_app_config('telemetry')
        self.assertEqual(app_config.name, 'telemetry')
        
        # Test that the app's default auto field matches global setting if set
        if hasattr(settings, 'DEFAULT_AUTO_FIELD'):
            # If global setting exists, app should use it or override it explicitly
            self.assertTrue(
                app_config.default_auto_field == settings.DEFAULT_AUTO_FIELD or
                app_config.default_auto_field == "django.db.models.BigAutoField"
            )
