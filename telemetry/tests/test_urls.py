"""
Unit and integration tests for telemetry URLs

Use --settings=app.test_settings for running tests

example:
python manage.py test telemetry.tests.test_urls --settings=app.test_settings
"""
import json
from django.test import TestCase, tag
from django.urls import reverse, resolve
from django.contrib.auth.models import User
from django.utils import timezone

from telemetry.models import Telemetry
from telemetry.views import TelemetryView, get_entries
from device_manager.models import Device
from fields.models import Field
from urllib.parse import unquote


class BaseTelemetryUrlTest(TestCase):
    """Base class for telemetry URL tests with common setup"""
    
    def setUp(self):
        """Set up test data"""
        # Create test user
        self.user = User.objects.create_user(
            username='testuser',
            password='testpassword',
            email='<EMAIL>'
        )
        
        # Create test field
        self.field = Field.objects.create(
            name="Test Field",
            cord=[{"lat": 0, "lng": 0}],
            colr="#FF0000",
            covr=10.0,
            loca="Test Location"
        )
        
        # Create test device
        self.device = Device.objects.create(
            name="Test Device",
            desc="Test Description",
            euid="ABCDEF0123456789",
            stat="Online",
            temp=25,
            batt=80,
            chrg=False,
            actv=True,
            mntc=False,
            hidn=False,
            attr={"client": {"temp": 25, "batt": 80}},
            type="Whiskers Node V1",
            aset="Battery",
            loca={"lati": 0.0, "long": 0.0, "alti": 0.0, "oofi": False, "plac": "Outdoor"},
            fild=self.field,
            offp=10
        )


@tag('unit')
class TelemetryUrlUnitTests(BaseTelemetryUrlTest):
    """Unit tests for telemetry URL configuration"""
    
    def test_telemetry_app_name(self):
        """Test that telemetry URLs have correct app_name"""
        from telemetry.urls import app_name
        self.assertEqual(app_name, "telemetry")
        
    def test_viewer_url_pattern(self):
        """Test viewer URL pattern configuration"""
        url = reverse('telemetry:viewer', kwargs={
            'device_id': '123',
            'key': 'temperature',
            'title': 'Temperature Sensor'
        })
        
        expected_url = '/telemetry/viewer/123/temperature/Temperature Sensor'
        self.assertEqual(unquote(url), expected_url)
        
    def test_get_entries_url_pattern(self):
        """Test get-entries URL pattern configuration"""
        url = reverse('telemetry:get-entries', kwargs={
            'device_id': '123',
            'key': 'temperature'
        })
        
        expected_url = '/telemetry/get-entries/123/temperature/'
        self.assertEqual(url, expected_url)
        
    def test_viewer_url_resolution(self):
        """Test that viewer URL resolves to correct view"""
        url = reverse('telemetry:viewer', kwargs={
            'device_id': '123',
            'key': 'temperature',
            'title': 'Temperature'
        })
        
        resolved = resolve(url)
        self.assertEqual(resolved.func.view_class, TelemetryView)
        self.assertEqual(resolved.kwargs['device_id'], '123')
        self.assertEqual(resolved.kwargs['key'], 'temperature')
        self.assertEqual(resolved.kwargs['title'], 'Temperature')
        
    def test_get_entries_url_resolution(self):
        """Test that get-entries URL resolves to correct view"""
        url = reverse('telemetry:get-entries', kwargs={
            'device_id': '123',
            'key': 'temperature'
        })
        
        resolved = resolve(url)
        self.assertEqual(resolved.func, get_entries)
        self.assertEqual(resolved.kwargs['device_id'], '123')
        self.assertEqual(resolved.kwargs['key'], 'temperature')
        
    def test_url_parameter_types(self):
        """Test that URL parameters are treated as strings"""
        # Test with numeric device_id
        url = reverse('telemetry:viewer', kwargs={
            'device_id': '12345',
            'key': 'temp',
            'title': 'Temperature'
        })
        
        resolved = resolve(url)
        self.assertIsInstance(resolved.kwargs['device_id'], str)
        self.assertEqual(resolved.kwargs['device_id'], '12345')
        
    def test_url_special_characters(self):
        """Test URLs with special characters"""
        # Test with spaces and special characters in title
        url = reverse('telemetry:viewer', kwargs={
            'device_id': '123',
            'key': 'air_temp',
            'title': 'Air Temperature Sensor'
        })
        
        resolved = resolve(url)
        self.assertEqual(unquote(resolved.kwargs['title']), 'Air Temperature Sensor')
        
    def test_url_case_sensitivity(self):
        """Test URL case sensitivity"""
        url1 = reverse('telemetry:viewer', kwargs={
            'device_id': '123',
            'key': 'Temperature',
            'title': 'TEMP'
        })
        
        url2 = reverse('telemetry:viewer', kwargs={
            'device_id': '123',
            'key': 'temperature',
            'title': 'temp'
        })
        
        # URLs should be different (case sensitive)
        self.assertNotEqual(url1, url2)
        
        # Both should resolve correctly
        resolved1 = resolve(url1)
        resolved2 = resolve(url2)
        
        self.assertEqual(resolved1.kwargs['key'], 'Temperature')
        self.assertEqual(resolved2.kwargs['key'], 'temperature')


@tag('integration')
class TelemetryUrlIntegrationTests(BaseTelemetryUrlTest):
    """Integration tests for telemetry URLs with real HTTP requests"""
    
    def test_viewer_url_http_get(self):
        """Test viewer URL with HTTP GET request"""
        self.client.login(username='testuser', password='testpassword')
        
        url = reverse('telemetry:viewer', kwargs={
            'device_id': str(self.device.id),
            'key': 'temperature',
            'title': 'Temperature'
        })
        
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        
    def test_get_entries_url_http_get(self):
        """Test get-entries URL with HTTP GET request"""
        self.client.login(username='testuser', password='testpassword')
        
        url = reverse('telemetry:get-entries', kwargs={
            'device_id': str(self.device.id),
            'key': 'temperature'
        })
        
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/json')
        
    def test_url_authentication_redirect(self):
        """Test that URLs redirect to login when not authenticated"""
        # Test viewer URL
        viewer_url = reverse('telemetry:viewer', kwargs={
            'device_id': str(self.device.id),
            'key': 'temperature',
            'title': 'Temperature'
        })
        
        response = self.client.get(viewer_url)
        self.assertEqual(response.status_code, 302)
        self.assertIn('/accounts/login/', response.url)
        
        # Test get-entries URL
        entries_url = reverse('telemetry:get-entries', kwargs={
            'device_id': str(self.device.id),
            'key': 'temperature'
        })
        
        response = self.client.get(entries_url)
        self.assertEqual(response.status_code, 302)
        self.assertIn('/accounts/login/', response.url)
        
    def test_url_with_real_device_data(self):
        """Test URLs with real device and telemetry data"""
        # Create telemetry data
        Telemetry.objects.create(
            devi=self.device,
            key='temperature',
            value='25.5',
            datetime=timezone.now()
        )
        
        self.client.login(username='testuser', password='testpassword')
        
        # Test viewer URL
        viewer_url = reverse('telemetry:viewer', kwargs={
            'device_id': str(self.device.id),
            'key': 'temperature',
            'title': 'Temperature Data'
        })
        
        response = self.client.get(viewer_url)
        self.assertEqual(response.status_code, 200)
        
        # Test get-entries URL
        entries_url = reverse('telemetry:get-entries', kwargs={
            'device_id': str(self.device.id),
            'key': 'temperature'
        })
        
        response = self.client.get(entries_url)
        self.assertEqual(response.status_code, 200)
        
        # Should return the telemetry data
        import json
        data = json.loads(response.content)
        self.assertEqual(len(data), 1)
        self.assertEqual(data[0]['value'], '25.5')
        
    def test_url_with_nonexistent_device(self):
        """Test URLs with non-existent device ID"""
        self.client.login(username='testuser', password='testpassword')
        
        # Test viewer URL - should still load (view doesn't validate device existence)
        viewer_url = reverse('telemetry:viewer', kwargs={
            'device_id': '99999',
            'key': 'temperature',
            'title': 'Temperature'
        })
        
        response = self.client.get(viewer_url)
        self.assertEqual(response.status_code, 200)
        
        # Test get-entries URL - should return empty data
        entries_url = reverse('telemetry:get-entries', kwargs={
            'device_id': '99999',
            'key': 'temperature'
        })
        
        response = self.client.get(entries_url)
        self.assertEqual(response.status_code, 200)
        
        import json
        data = json.loads(response.content)
        self.assertEqual(data, [])
        
    def test_url_parameter_encoding(self):
        """Test URL parameter encoding with special characters"""
        self.client.login(username='testuser', password='testpassword')
        
        # Test with URL-encoded characters
        url = reverse('telemetry:viewer', kwargs={
            'device_id': str(self.device.id),
            'key': 'air_temperature',
            'title': 'Air Temperature & Humidity'
        })
        
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)


@tag('e2e')
class TelemetryUrlE2ETests(BaseTelemetryUrlTest):
    """End-to-end tests for telemetry URL workflows"""
    
    def test_complete_telemetry_viewing_workflow(self):
        """Test complete workflow from URL to data display"""
        # Create telemetry data
        telemetry_data = []
        for i in range(5):
            telemetry_data.append(Telemetry.objects.create(
                devi=self.device,
                key='temperature',
                value=str(20 + i),
                datetime=timezone.now()
            ))
            
        self.client.login(username='testuser', password='testpassword')
        
        # Step 1: Access viewer page
        viewer_url = reverse('telemetry:viewer', kwargs={
            'device_id': str(self.device.id),
            'key': 'temperature',
            'title': 'Temperature Monitoring'
        })
        
        viewer_response = self.client.get(viewer_url)
        self.assertEqual(viewer_response.status_code, 200)
        
        # Step 2: Get telemetry data via API
        entries_url = reverse('telemetry:get-entries', kwargs={
            'device_id': str(self.device.id),
            'key': 'temperature'
        })
        
        entries_response = self.client.get(entries_url)
        self.assertEqual(entries_response.status_code, 200)
        
        # Step 3: Verify data integrity
        import json
        data = json.loads(entries_response.content)
        self.assertEqual(len(data), 5)
        
        # Data should be ordered by datetime
        values = [record['value'] for record in data]
        self.assertEqual(values, ['20', '21', '22', '23', '24'])
        
    def test_multiple_device_url_isolation(self):
        """Test URL isolation between multiple devices"""
        # Create second device
        device2 = Device.objects.create(
            name="Test Device 2",
            desc="Second Device",
            euid="FEDCBA9876543210",
            stat="Online",
            fild=self.field,
            offp=1
        )
        
        # Create telemetry for both devices
        Telemetry.objects.create(
            devi=self.device,
            key='temperature',
            value='25',
            datetime=timezone.now()
        )
        
        Telemetry.objects.create(
            devi=device2,
            key='temperature',
            value='30',
            datetime=timezone.now()
        )
        
        self.client.login(username='testuser', password='testpassword')
        
        # Test URLs for first device
        url1 = reverse('telemetry:get-entries', kwargs={
            'device_id': str(self.device.id),
            'key': 'temperature'
        })
        
        response1 = self.client.get(url1)
        data1 = json.loads(response1.content)
        
        # Test URLs for second device
        url2 = reverse('telemetry:get-entries', kwargs={
            'device_id': str(device2.id),
            'key': 'temperature'
        })
        
        response2 = self.client.get(url2)
        data2 = json.loads(response2.content)
        
        # Verify data isolation
        self.assertEqual(len(data1), 1)
        self.assertEqual(len(data2), 1)
        self.assertEqual(data1[0]['value'], '25')
        self.assertEqual(data2[0]['value'], '30')
        
    def test_url_error_handling(self):
        """Test URL error handling for various edge cases"""
        self.client.login(username='testuser', password='testpassword')
        
        # Test with invalid device ID format (should still work as string)
        url = reverse('telemetry:get-entries', kwargs={
            'device_id': '00000000', #invalid device ID
            'key': 'temperature'
        })
        
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.content)
        self.assertEqual(data, [])  # Should return empty list
