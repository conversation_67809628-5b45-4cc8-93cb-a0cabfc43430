"""
End-to-end tests for telemetry module

Use --settings=app.test_settings for running tests

example:
python manage.py test telemetry.tests.test_e2e --settings=app.test_settings
"""

import json
import time
from django.test import TestCase, Client, tag, TransactionTestCase
from django.urls import reverse
from django.contrib.auth.models import User
from django.utils import timezone
from django.db import transaction
from datetime import datetime, timedelta
from unittest.mock import patch, Mock

from telemetry.models import Telemetry
from telemetry.scripts import generate_telemetries, batch_generate_telemetries, process_sensory
from device_manager.models import Device
from fields.models import Field


class BaseTelemetryE2ETest(TransactionTestCase):
    """Base class for telemetry E2E tests with common setup"""
    
    def setUp(self):
        """Set up test data"""
        # Create test users
        self.admin_user = User.objects.create_superuser(
            username='admin',
            email='<EMAIL>',
            password='adminpassword'
        )
        
        self.regular_user = User.objects.create_user(
            username='user',
            password='userpassword',
            email='<EMAIL>'
        )
        
        # Create test field
        self.field = Field.objects.create(
            name="Production Field",
            cord=[{"lat": 40.7128, "lng": -74.0060}],
            colr="#00FF00",
            covr=25.5,
            loca="New York City"
        )
        
        # Create test devices simulating real IoT devices
        self.gateway_device = Device.objects.create(
            name="Gateway Device 001",
            desc="Main gateway for sensor network",
            euid="GATEWAY001234567",
            stat="Online",
            temp=22,
            batt=100,
            chrg=True,
            actv=True,
            mntc=False,
            hidn=False,
            attr={
                "client": {
                    "temp": 22.5,
                    "txok": 1500,
                    "txin": 1520,
                    "ackr": 98.5,
                    "rxok": 1480,
                    "rxfw": 1475,
                    "IP": "*************"
                }
            },
            type="Whiskers Gateway V1",
            aset="Infrastructure",
            loca={"lati": 40.7128, "long": -74.0060, "alti": 10, "oofi": False, "plac": "Outdoor"},
            fild=self.field,
            offp=300,
            lupd=timezone.now()
        )
        
        self.sensor_device = Device.objects.create(
            name="Environmental Sensor 001",
            desc="Temperature and humidity sensor",
            euid="SENSOR0123456789",
            stat="Online",
            temp=25,
            batt=85,
            chrg=False,
            actv=True,
            mntc=False,
            hidn=False,
            attr={
                "client": {
                    "Air Temperature": 25.3,
                    "Battery": 85,
                    "Light": 750,
                    "Positioning Status": "Fixed",
                    "Frame Counter": 12450,
                    "Motion event.": False,
                    "Motionless event.": True,
                    "Shock event.": False,
                    "RSSI": -65
                }
            },
            type="Whiskers Node V1",
            aset="Environmental",
            loca={"lati": 40.7132, "long": -74.0058, "alti": 15, "oofi": False, "plac": "Outdoor"},
            fild=self.field,
            offp=600,
            lupd=timezone.now()
        )
        
        self.client = Client()


@tag('e2e')
class TelemetryCompleteWorkflowE2ETests(BaseTelemetryE2ETest):
    """End-to-end tests for complete telemetry workflows"""
    
    def test_complete_iot_data_pipeline(self):
        """Test complete IoT data pipeline from sensor to visualization"""
        # Step 1: Simulate IoT device sending sensor data
        sensor_data = {
            "batt": 82,
            "temp": 26,
            "lati": 40.7132,
            "long": -74.0056
        }
        
        # Process sensory data (simulating data ingestion)
        process_sensory(self.sensor_device, sensor_data)
        
        # Update device attributes to reflect new sensor readings
        self.sensor_device.attr["client"]["Air Temperature"] = 26.0
        self.sensor_device.attr["client"]["Battery"] = 82
        self.sensor_device.save()
        
        # Step 2: Generate telemetry records (simulating data processing)
        telemetries = ["Air Temperature", "Battery", "Light", "RSSI"]
        generate_telemetries(self.sensor_device, telemetries)
        
        # Step 3: Verify data persistence
        db_records = Telemetry.objects.filter(devi=self.sensor_device)
        self.assertEqual(db_records.count(), 4)
        
        # Step 4: User authentication and access
        self.client.login(username='user', password='userpassword')
        
        # Step 5: Access telemetry viewer (simulating user dashboard access)
        viewer_url = reverse('telemetry:viewer', kwargs={
            'device_id': str(self.sensor_device.id),
            'key': 'Air Temperature',
            'title': 'Environmental Temperature Monitor'
        })
        
        viewer_response = self.client.get(viewer_url)
        self.assertEqual(viewer_response.status_code, 200)
        
        # Verify viewer context
        self.assertEqual(viewer_response.context['device_id'], str(self.sensor_device.id))
        self.assertEqual(viewer_response.context['key'], 'Air Temperature')
        self.assertEqual(viewer_response.context['title'], 'Environmental Temperature Monitor')
        
        # Step 6: Fetch telemetry data via API (simulating dashboard data loading)
        api_url = reverse('telemetry:get-entries', kwargs={
            'device_id': str(self.sensor_device.id),
            'key': 'Air Temperature'
        })
        
        api_response = self.client.get(api_url)
        self.assertEqual(api_response.status_code, 200)
        
        # Step 7: Verify data integrity throughout pipeline
        data = json.loads(api_response.content)
        self.assertEqual(len(data), 1)
        self.assertEqual(data[0]['key'], 'Air Temperature')
        self.assertEqual(data[0]['value'], '26.0')
        
        # Step 8: Verify device state was updated
        updated_device = Device.objects.get(id=self.sensor_device.id)
        self.assertEqual(updated_device.batt, 82)
        self.assertEqual(updated_device.temp, 26)
        self.assertEqual(updated_device.loca.get('lati'), 40.7132)
        self.assertEqual(updated_device.loca.get('long'), -74.0056)
        
    def test_multi_device_monitoring_scenario(self):
        """Test monitoring multiple devices simultaneously"""
        # Step 1: Generate historical data for both devices
        base_time = timezone.now() - timedelta(hours=24)
        
        # Gateway device telemetries (hourly for 24 hours)
        gateway_telemetries = ["temp", "txok", "rxok", "ackr"]
        for hour in range(24):
            timestamp = base_time + timedelta(hours=hour)
            
            # Update device attributes with time-varying data
            self.gateway_device.attr["client"]["temp"] = 20 + (hour % 12)
            self.gateway_device.attr["client"]["txok"] = 1500 + hour * 10
            self.gateway_device.attr["client"]["rxok"] = 1480 + hour * 8
            self.gateway_device.attr["client"]["ackr"] = 98.0 + (hour % 3) * 0.5
            self.gateway_device.lupd = timestamp
            
            # Create telemetry records
            for key in gateway_telemetries:
                Telemetry.objects.create(
                    devi=self.gateway_device,
                    key=key,
                    value=str(self.gateway_device.attr["client"][key]),
                    datetime=timestamp
                )
                
        # Sensor device telemetries (every 15 minutes for 24 hours)
        sensor_telemetries = ["Air Temperature", "Battery", "Light"]
        for quarter_hour in range(96):  # 24 hours * 4 quarters
            timestamp = base_time + timedelta(minutes=quarter_hour * 15)
            
            # Update device attributes with realistic sensor variations
            temp_variation = 25 + 5 * (quarter_hour % 8) / 8  # Temperature cycle
            battery_drain = max(85 - quarter_hour * 0.1, 20)  # Battery drain
            light_cycle = 800 if 6 <= (quarter_hour // 4) % 24 <= 18 else 50  # Day/night cycle
            
            self.sensor_device.attr["client"]["Air Temperature"] = temp_variation
            self.sensor_device.attr["client"]["Battery"] = battery_drain
            self.sensor_device.attr["client"]["Light"] = light_cycle
            self.sensor_device.lupd = timestamp
            
            for key in sensor_telemetries:
                Telemetry.objects.create(
                    devi=self.sensor_device,
                    key=key,
                    value=str(self.sensor_device.attr["client"][key]),
                    datetime=timestamp
                )
                
        # Step 2: User accesses monitoring dashboard
        self.client.login(username='user', password='userpassword')
        
        # Step 3: Monitor gateway device
        gateway_temp_url = reverse('telemetry:get-entries', kwargs={
            'device_id': str(self.gateway_device.id),
            'key': 'temp'
        })
        
        gateway_response = self.client.get(gateway_temp_url)
        gateway_data = json.loads(gateway_response.content)
        
        self.assertEqual(len(gateway_data), 24)  # 24 hourly readings
        
        # Step 4: Monitor sensor device
        sensor_temp_url = reverse('telemetry:get-entries', kwargs={
            'device_id': str(self.sensor_device.id),
            'key': 'Air Temperature'
        })
        
        sensor_response = self.client.get(sensor_temp_url)
        sensor_data = json.loads(sensor_response.content)
        
        self.assertEqual(len(sensor_data), 96)  # 96 quarter-hourly readings
        
        # Step 5: Verify data ordering and integrity
        # Gateway data should be ordered by datetime
        gateway_values = [float(record['value']) for record in gateway_data]
        self.assertTrue(all(20 <= val <= 31 for val in gateway_values))
        
        # Sensor data should show realistic patterns
        sensor_values = [float(record['value']) for record in sensor_data]
        self.assertTrue(all(25 <= val <= 30 for val in sensor_values))
        
    def test_real_time_monitoring_simulation(self):
        """Test real-time monitoring simulation with continuous data updates"""
        self.client.login(username='user', password='userpassword')
        
        # Step 1: Initial state - no telemetry data
        initial_url = reverse('telemetry:get-entries', kwargs={
            'device_id': str(self.sensor_device.id),
            'key': 'Air Temperature'
        })
        
        initial_response = self.client.get(initial_url)
        initial_data = json.loads(initial_response.content)
        self.assertEqual(len(initial_data), 0)
        
        # Step 2: Simulate real-time data updates
        for minute in range(10):  # 10 minutes of data
            # Simulate sensor reading
            current_temp = 25 + minute * 0.5  # Gradual temperature increase
            current_time = timezone.now() + timedelta(minutes=minute)
            
            # Update device
            self.sensor_device.attr["client"]["Air Temperature"] = current_temp
            self.sensor_device.lupd = current_time
            
            # Generate telemetry
            Telemetry.objects.create(
                devi=self.sensor_device,
                key="Air Temperature",
                value=str(current_temp),
                datetime=current_time
            )
            
            # Check data availability via API
            response = self.client.get(initial_url)
            data = json.loads(response.content)
            
            # Should have minute + 1 records
            self.assertEqual(len(data), minute + 1)
            
            # Latest record should match current reading
            latest_record = data[-1]
            self.assertEqual(float(latest_record['value']), current_temp)
            
        # Step 3: Verify complete dataset
        final_response = self.client.get(initial_url)
        final_data = json.loads(final_response.content)
        
        self.assertEqual(len(final_data), 10)
        
        # Verify temperature progression
        temperatures = [float(record['value']) for record in final_data]
        expected_temps = [25 + i * 0.5 for i in range(10)]
        self.assertEqual(temperatures, expected_temps)
        
    def test_user_access_control_workflow(self):
        """Test complete user access control workflow"""
        # Create telemetry data
        Telemetry.objects.create(
            devi=self.sensor_device,
            key="Air Temperature",
            value="25.0",
            datetime=timezone.now()
        )
        
        viewer_url = reverse('telemetry:viewer', kwargs={
            'device_id': str(self.sensor_device.id),
            'key': 'Air Temperature',
            'title': 'Temperature'
        })
        
        api_url = reverse('telemetry:get-entries', kwargs={
            'device_id': str(self.sensor_device.id),
            'key': 'Air Temperature'
        })
        
        # Step 1: Anonymous user access (should be denied)
        viewer_response = self.client.get(viewer_url)
        api_response = self.client.get(api_url)
        
        self.assertEqual(viewer_response.status_code, 302)  # Redirect to login
        self.assertEqual(api_response.status_code, 302)     # Redirect to login
        
        # Step 2: Regular user access (should be allowed)
        self.client.login(username='user', password='userpassword')
        
        viewer_response = self.client.get(viewer_url)
        api_response = self.client.get(api_url)
        
        self.assertEqual(viewer_response.status_code, 200)
        self.assertEqual(api_response.status_code, 200)
        
        # Verify data access
        data = json.loads(api_response.content)
        self.assertEqual(len(data), 1)
        self.assertEqual(data[0]['value'], '25.0')
        
        # Step 3: Admin user access (should be allowed)
        self.client.logout()
        self.client.login(username='admin', password='adminpassword')
        
        viewer_response = self.client.get(viewer_url)
        api_response = self.client.get(api_url)
        
        self.assertEqual(viewer_response.status_code, 200)
        self.assertEqual(api_response.status_code, 200)
        
        # Admin should see same data
        admin_data = json.loads(api_response.content)
        self.assertEqual(admin_data, data)
        
    def test_error_handling_and_recovery_workflow(self):
        """Test error handling and recovery in complete workflow"""
        self.client.login(username='user', password='userpassword')
        
        # Step 1: Test with non-existent device
        invalid_device_url = reverse('telemetry:get-entries', kwargs={
            'device_id': '99999',
            'key': 'temperature'
        })
        
        response = self.client.get(invalid_device_url)
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.content)
        self.assertEqual(data, [])  # Should return empty list
        
        # Step 2: Test with non-existent telemetry key
        nonexistent_key_url = reverse('telemetry:get-entries', kwargs={
            'device_id': str(self.sensor_device.id),
            'key': 'nonexistent_sensor'
        })
        
        response = self.client.get(nonexistent_key_url)
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.content)
        self.assertEqual(data, [])  # Should return empty list
        
        # Step 3: Test recovery after adding data
        Telemetry.objects.create(
            devi=self.sensor_device,
            key="nonexistent_sensor",
            value="recovery_value",
            datetime=timezone.now()
        )
        
        response = self.client.get(nonexistent_key_url)
        data = json.loads(response.content)
        
        self.assertEqual(len(data), 1)
        self.assertEqual(data[0]['value'], 'recovery_value')
        
        # Step 4: Test viewer with invalid parameters
        invalid_viewer_url = reverse('telemetry:viewer', kwargs={
            'device_id': '99999',
            'key': 'invalid',
            'title': 'Invalid Test'
        })
        
        response = self.client.get(invalid_viewer_url)
        self.assertEqual(response.status_code, 200)  # Viewer should still load
        
        # Context should still be passed correctly
        self.assertEqual(response.context['device_id'], '99999')
        self.assertEqual(response.context['key'], 'invalid')
        self.assertEqual(response.context['title'], 'Invalid Test')
