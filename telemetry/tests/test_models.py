"""
Unit tests for telemetry models

Use --settings=app.test_settings for running tests

example:
python manage.py test telemetry.tests.test_models --settings=app.test_settings
"""

from django.test import TestCase, tag
from django.utils import timezone
from django.core.exceptions import ValidationError
from unittest.mock import patch, Mock
from datetime import datetime, timedelta

from telemetry.models import Telemetry
from device_manager.models import Device
from fields.models import Field


class BaseTelemetryModelTest(TestCase):
    """Base class for telemetry model tests with common setup"""
    
    def setUp(self):
        """Set up test data"""
        # Create a test field
        self.field = Field.objects.create(
            name="Test Field",
            cord=[{"lat": 0, "lng": 0}],
            colr="#FF0000",
            covr=10.0,
            loca="Test Location"
        )
        
        # Create test device
        self.device = Device.objects.create(
            name="Test Device",
            desc="Test Description",
            euid="ABCDEF0123456789",
            stat="Online",
            temp=25,
            batt=80,
            chrg=False,
            actv=True,
            mntc=False,
            hidn=False,
            attr={"client": {"temp": 25, "batt": 80, "Motion event.": False}},
            type="Whiskers Node V1",
            aset="Battery",
            loca={"lati": 0.0, "long": 0.0, "alti": 0.0, "oofi": False, "plac": "Outdoor"},
            fild=self.field,
            offp=10
        )
        
        # Create test telemetry data
        self.test_datetime = timezone.now()
        self.telemetry_data = {
            'devi': self.device,
            'key': 'temperature',
            'value': '25.5',
            'datetime': self.test_datetime
        }


@tag('unit')
class TelemetryModelUnitTests(BaseTelemetryModelTest):
    """Unit tests for Telemetry model"""
    
    def test_telemetry_creation_basic_attributes(self):
        """Test that a telemetry record can be created with basic attributes"""
        telemetry = Telemetry.objects.create(**self.telemetry_data)
        
        self.assertEqual(telemetry.devi, self.device)
        self.assertEqual(telemetry.key, 'temperature')
        self.assertEqual(telemetry.value, '25.5')
        self.assertEqual(telemetry.datetime, self.test_datetime)
        
    def test_telemetry_string_representation(self):
        """Test the string representation of telemetry model"""
        telemetry = Telemetry.objects.create(**self.telemetry_data)
        # Since there's no __str__ method defined, it should use default
        expected_str = f"Telemetry object ({telemetry.id})"
        self.assertEqual(str(telemetry), expected_str)
        
    def test_telemetry_device_relationship(self):
        """Test the foreign key relationship with Device"""
        telemetry = Telemetry.objects.create(**self.telemetry_data)
        
        # Test forward relationship
        self.assertEqual(telemetry.devi.name, "Test Device")
        self.assertEqual(telemetry.devi.euid, "ABCDEF0123456789")
        
        # Test reverse relationship
        device_telemetries = self.device.telemetry_set.all()
        self.assertIn(telemetry, device_telemetries)
        
    def test_telemetry_key_max_length(self):
        """Test that key field respects max_length constraint"""
        long_key = 'a' * 256  # 256 characters, exceeds max_length of 255
        telemetry_data = self.telemetry_data.copy()
        telemetry_data['key'] = long_key
        
        telemetry = Telemetry(**telemetry_data)
        with self.assertRaises(ValidationError):
            telemetry.full_clean()
            
    def test_telemetry_value_max_length(self):
        """Test that value field respects max_length constraint"""
        long_value = 'a' * 256  # 256 characters, exceeds max_length of 255
        telemetry_data = self.telemetry_data.copy()
        telemetry_data['value'] = long_value
        
        telemetry = Telemetry(**telemetry_data)
        with self.assertRaises(ValidationError):
            telemetry.full_clean()
            
    def test_telemetry_required_fields(self):
        """Test that all required fields are properly validated"""
        # Test missing device
        with self.assertRaises(ValidationError):
            telemetry = Telemetry(key='test', value='test', datetime=timezone.now())
            telemetry.full_clean()
            
        # Test missing key
        with self.assertRaises(ValidationError):
            telemetry = Telemetry(devi=self.device, value='test', datetime=timezone.now())
            telemetry.full_clean()
            
        # Test missing value
        with self.assertRaises(ValidationError):
            telemetry = Telemetry(devi=self.device, key='test', datetime=timezone.now())
            telemetry.full_clean()
            
        # Test missing datetime
        with self.assertRaises(ValidationError):
            telemetry = Telemetry(devi=self.device, key='test', value='test')
            telemetry.full_clean()
            
    def test_telemetry_cascade_delete(self):
        """Test that telemetry records are deleted when device is deleted"""
        telemetry = Telemetry.objects.create(**self.telemetry_data)
        telemetry_id = telemetry.id
        
        # Verify telemetry exists
        self.assertTrue(Telemetry.objects.filter(id=telemetry_id).exists())
        
        # Delete device
        self.device.delete()
        
        # Verify telemetry is also deleted due to CASCADE
        self.assertFalse(Telemetry.objects.filter(id=telemetry_id).exists())
        
    def test_telemetry_different_data_types(self):
        """Test telemetry with different value data types"""
        test_cases = [
            ('temperature', '25.5'),
            ('battery', '80'),
            ('status', 'online'),
            ('motion', 'true'),
            ('coordinates', '{"lat": 12.34, "lng": 56.78}'),
            ('empty_value', ''),
        ]
        
        for key, value in test_cases:
            with self.subTest(key=key, value=value):
                telemetry_data = self.telemetry_data.copy()
                telemetry_data['key'] = key
                telemetry_data['value'] = value
                
                telemetry = Telemetry.objects.create(**telemetry_data)
                self.assertEqual(telemetry.key, key)
                self.assertEqual(telemetry.value, value)
                
    def test_telemetry_datetime_ordering(self):
        """Test that telemetry records can be ordered by datetime"""
        # Create multiple telemetry records with different timestamps
        base_time = timezone.now()
        telemetries = []
        
        for i in range(3):
            telemetry_data = self.telemetry_data.copy()
            telemetry_data['key'] = f'test_key_{i}'
            telemetry_data['datetime'] = base_time + timedelta(minutes=i)
            telemetries.append(Telemetry.objects.create(**telemetry_data))
            
        # Test ordering by datetime
        ordered_telemetries = Telemetry.objects.filter(devi=self.device).order_by('datetime')
        self.assertEqual(list(ordered_telemetries), telemetries)
        
        # Test reverse ordering
        reverse_ordered = Telemetry.objects.filter(devi=self.device).order_by('-datetime')
        self.assertEqual(list(reverse_ordered), list(reversed(telemetries)))


@tag('integration')
class TelemetryModelIntegrationTests(BaseTelemetryModelTest):
    """Integration tests for Telemetry model with real database operations"""
    
    def test_bulk_telemetry_creation(self):
        """Test creating multiple telemetry records efficiently"""
        telemetry_records = []
        base_time = timezone.now()
        
        for i in range(100):
            telemetry_records.append(Telemetry(
                devi=self.device,
                key=f'sensor_{i % 10}',
                value=str(i * 0.5),
                datetime=base_time + timedelta(seconds=i)
            ))
            
        # Bulk create
        created_records = Telemetry.objects.bulk_create(telemetry_records)
        self.assertEqual(len(created_records), 100)
        
        # Verify records exist in database
        db_count = Telemetry.objects.filter(devi=self.device).count()
        self.assertEqual(db_count, 100)
        
    def test_telemetry_filtering_by_device_and_key(self):
        """Test filtering telemetry records by device and key"""
        # Create telemetry records for different keys
        keys = ['temperature', 'battery', 'humidity']
        for key in keys:
            for i in range(5):
                Telemetry.objects.create(
                    devi=self.device,
                    key=key,
                    value=str(i),
                    datetime=timezone.now() + timedelta(minutes=i)
                )
                
        # Test filtering by key
        temp_records = Telemetry.objects.filter(devi=self.device, key='temperature')
        self.assertEqual(temp_records.count(), 5)
        
        battery_records = Telemetry.objects.filter(devi=self.device, key='battery')
        self.assertEqual(battery_records.count(), 5)
        
        # Test filtering by device
        all_device_records = Telemetry.objects.filter(devi=self.device)
        self.assertEqual(all_device_records.count(), 15)
        
    def test_telemetry_time_range_queries(self):
        """Test querying telemetry records within time ranges"""
        base_time = timezone.now()
        
        # Create records over a 24-hour period
        for hour in range(24):
            Telemetry.objects.create(
                devi=self.device,
                key='hourly_reading',
                value=str(hour),
                datetime=base_time + timedelta(hours=hour)
            )
            
        # Query records from last 12 hours
        twelve_hours_ago = base_time + timedelta(hours=12)
        recent_records = Telemetry.objects.filter(
            devi=self.device,
            datetime__gte=twelve_hours_ago
        )
        self.assertEqual(recent_records.count(), 12)
        
        # Query records from specific time range
        start_time = base_time + timedelta(hours=5)
        end_time = base_time + timedelta(hours=10)
        range_records = Telemetry.objects.filter(
            devi=self.device,
            datetime__gte=start_time,
            datetime__lte=end_time
        )
        self.assertEqual(range_records.count(), 6)  # hours 5, 6, 7, 8, 9, 10
