"""
Unit and integration tests for telemetry views

Use --settings=app.test_settings for running tests

example:
python manage.py test telemetry.tests.test_views --settings=app.test_settings
"""

import json
from django.test import TestCase, Client, tag
from django.urls import reverse
from django.contrib.auth.models import User
from django.utils import timezone
from django.http import HttpResponse
from unittest.mock import patch, Mock
from datetime import datetime, timedelta

from telemetry.models import Telemetry
from telemetry.views import TelemetryView, get_entries
from device_manager.models import Device
from fields.models import Field


class BaseTelemetryViewTest(TestCase):
    """Base class for telemetry view tests with common setup"""
    
    def setUp(self):
        """Set up test data"""
        # Create test user
        self.user = User.objects.create_user(
            username='testuser',
            password='testpassword',
            email='<EMAIL>'
        )
        
        # Create test field
        self.field = Field.objects.create(
            name="Test Field",
            cord=[{"lat": 0, "lng": 0}],
            colr="#FF0000",
            covr=10.0,
            loca="Test Location"
        )
        
        # Create test device
        self.device = Device.objects.create(
            name="Test Device",
            desc="Test Description",
            euid="ABCDEF0123456789",
            stat="Online",
            temp=25,
            batt=80,
            chrg=False,
            actv=True,
            mntc=False,
            hidn=False,
            attr={"client": {"temp": 25, "batt": 80}},
            type="Whiskers Node V1",
            aset="Battery",
            loca={"lati": 0.0, "long": 0.0, "alti": 0.0, "oofi": False, "plac": "Outdoor"},
            fild=self.field,
            offp=10
        )
        
        # Create test telemetry data
        self.base_time = timezone.now()
        self.telemetry_records = []
        for i in range(5):
            telemetry = Telemetry.objects.create(
                devi=self.device,
                key='temperature',
                value=str(20 + i),
                datetime=self.base_time + timedelta(minutes=i)
            )
            self.telemetry_records.append(telemetry)
            
        self.client = Client()


@tag('unit')
class TelemetryViewUnitTests(BaseTelemetryViewTest):
    """Unit tests for TelemetryView class"""
    
    def test_telemetry_view_requires_login(self):
        """Test that TelemetryView requires user authentication"""
        url = reverse('telemetry:viewer', kwargs={
            'device_id': str(self.device.id),
            'key': 'temperature',
            'title': 'Temperature'
        })
        
        # Test without login
        response = self.client.get(url)
        self.assertEqual(response.status_code, 302)  # Redirect to login
        self.assertIn('/accounts/login/', response.url)
        
    def test_telemetry_view_with_login(self):
        """Test TelemetryView with authenticated user"""
        self.client.login(username='testuser', password='testpassword')
        
        url = reverse('telemetry:viewer', kwargs={
            'device_id': str(self.device.id),
            'key': 'temperature',
            'title': 'Temperature'
        })
        
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        
    @patch('telemetry.views.render')
    def test_telemetry_view_context_data(self, mock_render):
        """Test that TelemetryView passes correct context data"""
        mock_render.return_value = HttpResponse("OK")
        self.client.login(username='testuser', password='testpassword')
        
        url = reverse('telemetry:viewer', kwargs={
            'device_id': str(self.device.id),
            'key': 'temperature',
            'title': 'Temperature Sensor'
        })
        
        response = self.client.get(url)
        
        # Verify render was called with correct context
        mock_render.assert_called_once()
        args, kwargs = mock_render.call_args
        
        request, template_name, context = args
        self.assertEqual(template_name, "telemetry/viewer.html")
        self.assertEqual(context['key'], 'temperature')
        self.assertEqual(context['title'], 'Temperature Sensor')
        self.assertEqual(context['device_id'], str(self.device.id))
        
    def test_telemetry_view_method_decorator(self):
        """Test that TelemetryView has proper method decorator"""
        # Check that the view class has login_required decorator
        self.assertTrue(hasattr(TelemetryView, 'dispatch'))
        
    def test_telemetry_view_inheritance(self):
        """Test that TelemetryView inherits from correct classes"""
        from django.contrib.auth.mixins import LoginRequiredMixin
        from django.views.generic import View
        
        self.assertTrue(issubclass(TelemetryView, LoginRequiredMixin))
        self.assertTrue(issubclass(TelemetryView, View))


@tag('unit')
class GetEntriesViewUnitTests(BaseTelemetryViewTest):
    """Unit tests for get_entries function view"""
    
    def test_get_entries_requires_login(self):
        """Test that get_entries requires user authentication"""
        url = reverse('telemetry:get-entries', kwargs={
            'device_id': str(self.device.id),
            'key': 'temperature'
        })
        
        # Test without login
        response = self.client.get(url)
        self.assertEqual(response.status_code, 302)  # Redirect to login
        
    def test_get_entries_with_login(self):
        """Test get_entries with authenticated user"""
        self.client.login(username='testuser', password='testpassword')
        
        url = reverse('telemetry:get-entries', kwargs={
            'device_id': str(self.device.id),
            'key': 'temperature'
        })
        
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/json')
        
    def test_get_entries_json_response_structure(self):
        """Test that get_entries returns properly structured JSON"""
        self.client.login(username='testuser', password='testpassword')
        
        url = reverse('telemetry:get-entries', kwargs={
            'device_id': str(self.device.id),
            'key': 'temperature'
        })
        
        response = self.client.get(url)
        data = json.loads(response.content)
        
        # Should return list of telemetry records
        self.assertIsInstance(data, list)
        self.assertEqual(len(data), 5)  # We created 5 records in setUp
        
        # Check structure of first record
        first_record = data[0]
        self.assertIn('key', first_record)
        self.assertIn('value', first_record)
        self.assertIn('datetime', first_record)
        
        self.assertEqual(first_record['key'], 'temperature')
        
    def test_get_entries_ordering(self):
        """Test that get_entries returns records ordered by datetime"""
        self.client.login(username='testuser', password='testpassword')
        
        url = reverse('telemetry:get-entries', kwargs={
            'device_id': str(self.device.id),
            'key': 'temperature'
        })
        
        response = self.client.get(url)
        data = json.loads(response.content)
        
        # Records should be ordered by datetime (ascending)
        values = [record['value'] for record in data]
        expected_values = ['20', '21', '22', '23', '24']  # Based on our setUp
        self.assertEqual(values, expected_values)
        
    def test_get_entries_nonexistent_device(self):
        """Test get_entries with non-existent device ID"""
        self.client.login(username='testuser', password='testpassword')
        
        url = reverse('telemetry:get-entries', kwargs={
            'device_id': '99999',  # Non-existent device ID
            'key': 'temperature'
        })
        
        response = self.client.get(url)
        data = json.loads(response.content)
        
        # Should return empty list for non-existent device
        self.assertEqual(data, [])
        
    def test_get_entries_nonexistent_key(self):
        """Test get_entries with non-existent key"""
        self.client.login(username='testuser', password='testpassword')
        
        url = reverse('telemetry:get-entries', kwargs={
            'device_id': str(self.device.id),
            'key': 'nonexistent_key'
        })
        
        response = self.client.get(url)
        data = json.loads(response.content)
        
        # Should return empty list for non-existent key
        self.assertEqual(data, [])
        
    @patch('telemetry.views.Telemetry.objects.filter')
    def test_get_entries_database_query(self, mock_filter):
        """Test that get_entries makes correct database query"""
        mock_queryset = Mock()
        mock_queryset.order_by.return_value = []
        mock_filter.return_value = mock_queryset
        
        self.client.login(username='testuser', password='testpassword')
        
        url = reverse('telemetry:get-entries', kwargs={
            'device_id': str(self.device.id),
            'key': 'temperature'
        })
        
        response = self.client.get(url)
        
        # Verify correct filter was called
        mock_filter.assert_called_once_with(devi=str(self.device.id), key='temperature')
        mock_queryset.order_by.assert_called_once_with('datetime')


@tag('integration')
class TelemetryViewIntegrationTests(BaseTelemetryViewTest):
    """Integration tests for telemetry views with real database operations"""
    
    def test_full_telemetry_viewer_workflow(self):
        """Test complete workflow of viewing telemetry data"""
        self.client.login(username='testuser', password='testpassword')
        
        # First, access the viewer page
        viewer_url = reverse('telemetry:viewer', kwargs={
            'device_id': str(self.device.id),
            'key': 'temperature',
            'title': 'Temperature Data'
        })
        
        viewer_response = self.client.get(viewer_url)
        self.assertEqual(viewer_response.status_code, 200)
        
        # Then, get the actual data
        entries_url = reverse('telemetry:get-entries', kwargs={
            'device_id': str(self.device.id),
            'key': 'temperature'
        })
        
        entries_response = self.client.get(entries_url)
        self.assertEqual(entries_response.status_code, 200)
        
        data = json.loads(entries_response.content)
        self.assertEqual(len(data), 5)
        
    def test_multiple_devices_telemetry_isolation(self):
        """Test that telemetry data is properly isolated between devices"""
        # Create second device
        device2 = Device.objects.create(
            name="Test Device 2",
            desc="Second Test Device",
            euid="FEDCBA9876543210",
            stat="Online",
            fild=self.field,
            offp=1
        )
        
        # Create telemetry for second device
        Telemetry.objects.create(
            devi=device2,
            key='temperature',
            value='30',
            datetime=timezone.now()
        )
        
        self.client.login(username='testuser', password='testpassword')
        
        # Get entries for first device
        url1 = reverse('telemetry:get-entries', kwargs={
            'device_id': str(self.device.id),
            'key': 'temperature'
        })
        response1 = self.client.get(url1)
        data1 = json.loads(response1.content)
        
        # Get entries for second device
        url2 = reverse('telemetry:get-entries', kwargs={
            'device_id': str(device2.id),
            'key': 'temperature'
        })
        response2 = self.client.get(url2)
        data2 = json.loads(response2.content)
        
        # Verify data isolation
        self.assertEqual(len(data1), 5)  # Original device has 5 records
        self.assertEqual(len(data2), 1)  # Second device has 1 record
        self.assertEqual(data2[0]['value'], '30')
        
    def test_large_dataset_performance(self):
        """Test view performance with large dataset"""
        # Create many telemetry records
        telemetry_records = []
        for i in range(1000):
            telemetry_records.append(Telemetry(
                devi=self.device,
                key='performance_test',
                value=str(i),
                datetime=timezone.now() + timedelta(seconds=i)
            ))
        
        Telemetry.objects.bulk_create(telemetry_records)
        
        self.client.login(username='testuser', password='testpassword')
        
        url = reverse('telemetry:get-entries', kwargs={
            'device_id': str(self.device.id),
            'key': 'performance_test'
        })
        
        # This should complete without timeout
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.content)
        self.assertEqual(len(data), 1000)
