"""
Unit and integration tests for telemetry scripts

Use --settings=app.test_settings for running tests

example:
python manage.py test telemetry.tests.test_scripts --settings=app.test_settings
"""

import threading
from django.test import TestCase, tag
from django.utils import timezone
from unittest.mock import patch, Mock, MagicMock
from datetime import datetime, timedelta

from telemetry.models import Telemetry
from telemetry.scripts import (
    get_telemetry_list, 
    process_sensory, 
    generate_telemetries, 
    batch_generate_telemetries,
    _thread_local
)
from device_manager.models import Device
from fields.models import Field


class BaseTelemetryScriptsTest(TestCase):
    """Base class for telemetry scripts tests with common setup"""
    
    def setUp(self):
        """Set up test data"""
        # Create test field
        self.field = Field.objects.create(
            name="Test Field",
            cord=[{"lat": 0, "lng": 0}],
            colr="#FF0000",
            covr=10.0,
            loca="Test Location"
        )
        
        # Create test device with telemetry data in attr
        self.device = Device.objects.create(
            name="Test Device",
            desc="Test Description",
            euid="ABCDEF0123456789",
            stat="Online",
            temp=25,
            batt=80,
            chrg=False,
            actv=True,
            mntc=False,
            hidn=False,
            attr={
                "client": {
                    "temp": 25.5,
                    "batt": 80,
                    "humidity": 65,
                    "pressure": 1013.25,
                    "Motion event.": False,
                    "Shock event.": True
                }
            },
            type="Whiskers Node V1",
            aset="Battery",
            loca={"lati": 12.34, "long": 56.78, "alti": 100, "oofi": False, "plac": "Outdoor"},
            fild=self.field,
            offp=10,
            lupd=timezone.now()
        )


@tag('unit')
class TelemetryScriptsUnitTests(BaseTelemetryScriptsTest):
    """Unit tests for telemetry scripts functions"""

    def tearDown(self):
        # Clear thread-local telemetry list to prevent cross-test pollution
        if hasattr(_thread_local, 'telemetry_list'):
            _thread_local.telemetry_list.clear()
    
    def test_get_telemetry_list_thread_local(self):
        """Test that get_telemetry_list returns thread-local storage"""
        # Test in main thread
        list1 = get_telemetry_list()
        list2 = get_telemetry_list()
        
        # Should return same list instance for same thread
        self.assertIs(list1, list2)
        self.assertIsInstance(list1, list)
        
        # Test that different threads get different lists
        other_thread_list = []
        
        def get_list_in_thread():
            other_thread_list.append(get_telemetry_list())
            
        thread = threading.Thread(target=get_list_in_thread)
        thread.start()
        thread.join()
        
        # Different thread should get different list instance
        self.assertIsNot(list1, other_thread_list[0])
        
    def test_process_sensory_battery_normal(self):
        """Test process_sensory with normal battery value"""
        sensory_data = {"batt": 75}
        
        process_sensory(self.device, sensory_data)
        
        self.assertEqual(self.device.batt, 75)
        
    def test_process_sensory_battery_over_100(self):
        """Test process_sensory caps battery at 100"""
        sensory_data = {"batt": 150}
        
        process_sensory(self.device, sensory_data)
        
        self.assertEqual(self.device.batt, 100)
        
    def test_process_sensory_battery_exactly_100(self):
        """Test process_sensory with battery exactly 100"""
        sensory_data = {"batt": 100}
        
        process_sensory(self.device, sensory_data)
        
        self.assertEqual(self.device.batt, 100)
        
    def test_process_sensory_temperature(self):
        """Test process_sensory with temperature data"""
        sensory_data = {"temp": 30}
        
        process_sensory(self.device, sensory_data)
        
        self.assertEqual(self.device.temp, 30)
        
    def test_process_sensory_latitude_valid(self):
        """Test process_sensory with valid latitude"""
        sensory_data = {"lati": 45.5}

        process_sensory(self.device, sensory_data)

        self.assertEqual(self.device.loca.get('lati'), 45.5)
        
    def test_process_sensory_latitude_zero(self):
        """Test process_sensory ignores zero latitude"""
        original_lati = self.device.loca.get('lati', 0)
        sensory_data = {"lati": 0.0}

        process_sensory(self.device, sensory_data)

        self.assertEqual(self.device.loca.get('lati', 0), original_lati)  # Should remain unchanged
        
    def test_process_sensory_longitude_valid(self):
        """Test process_sensory with valid longitude"""
        sensory_data = {"long": 123.45}

        process_sensory(self.device, sensory_data)

        self.assertEqual(self.device.loca.get('long'), 123.45)
        
    def test_process_sensory_longitude_zero(self):
        """Test process_sensory ignores zero longitude"""
        original_long = self.device.loca.get('long', 0)
        sensory_data = {"long": 0.0}

        process_sensory(self.device, sensory_data)

        self.assertEqual(self.device.loca.get('long', 0), original_long)  # Should remain unchanged
        
    def test_process_sensory_multiple_fields(self):
        """Test process_sensory with multiple sensor fields"""
        sensory_data = {
            "batt": 90,
            "temp": 28,
            "lati": 40.7128,
            "long": -74.0060
        }
        
        process_sensory(self.device, sensory_data)
        
        self.assertEqual(self.device.batt, 90)
        self.assertEqual(self.device.temp, 28)
        self.assertEqual(self.device.loca.get('lati'), 40.7128)
        self.assertEqual(self.device.loca.get('long'), -74.0060)
        
    def test_process_sensory_empty_data(self):
        """Test process_sensory with empty sensory data"""
        original_batt = self.device.batt
        original_temp = self.device.temp
        
        process_sensory(self.device, {})
        
        # Values should remain unchanged
        self.assertEqual(self.device.batt, original_batt)
        self.assertEqual(self.device.temp, original_temp)
        
    def test_generate_telemetries_basic(self):
        """Test generate_telemetries basic functionality"""
        telemetries = ['temp', 'batt']

        generate_telemetries(self.device, telemetries)

        telemetry_objs = Telemetry.objects.filter(devi=self.device)

        self.assertEqual(telemetry_objs.count(), 2)

        keys = [t.key for t in telemetry_objs]
        self.assertIn('temp', keys)
        self.assertIn('batt', keys)

    @patch('telemetry.scripts.Telemetry.objects.bulk_create')
    def test_generate_telemetries_mixed_keys(self, mock_bulk_create):
        """Test generate_telemetries with existing and non-existing keys"""
        telemetries = ['temp', 'nonexistent_key', 'batt']

        generate_telemetries(self.device, telemetries)

        mock_bulk_create.assert_called_once()
        args, _ = mock_bulk_create.call_args
        telemetry_objs = args[0]

        self.assertEqual(len(telemetry_objs), 2)

        keys = [t.key for t in telemetry_objs]
        self.assertIn('temp', keys)
        self.assertIn('batt', keys)
        self.assertNotIn('nonexistent_key', keys)

    @patch('telemetry.scripts.Telemetry.objects.bulk_create')
    def test_generate_telemetries_nonexistent_keys_only(self, mock_bulk_create):
        """Test generate_telemetries with only non-existing keys"""
        telemetries = ['foo', 'bar']

        generate_telemetries(self.device, telemetries)

        mock_bulk_create.assert_not_called()
        
    @patch('telemetry.scripts.get_telemetry_list')
    def test_generate_telemetries_nonexistent_keys(self, mock_get_list):
        """Test generate_telemetries with non-existent keys"""
        mock_telemetry_list = []
        mock_get_list.return_value = mock_telemetry_list
        
        telemetries = ['nonexistent_key', 'another_missing_key']
        
        generate_telemetries(self.device, telemetries)
        
        # Should not create any telemetry objects
        self.assertEqual(len(mock_telemetry_list), 0)
        
    @patch('telemetry.scripts.get_telemetry_list')
    def test_generate_telemetries_mixed_keys(self, mock_get_list):
        """Test generate_telemetries with mix of existing and non-existing keys"""
        mock_telemetry_list = []
        mock_get_list.return_value = mock_telemetry_list
        
        telemetries = ['temp', 'nonexistent_key', 'batt']
        
        generate_telemetries(self.device, telemetries)
        
        # Should create telemetry objects only for existing keys

        
        
        self.assertEqual(Telemetry.objects.filter(devi=self.device).count(), 2)
        
    @patch('telemetry.scripts.Telemetry.objects.bulk_create')
    def test_batch_generate_telemetries_basic(self, mock_bulk_create):
        """Test batch_generate_telemetries basic functionality"""
        telemetries = ['temp', 'batt', 'humidity']
        
        batch_generate_telemetries(self.device, telemetries)
        
        # Should call bulk_create once for small batch
        mock_bulk_create.assert_called_once()
        
        # Check the telemetry objects created
        call_args = mock_bulk_create.call_args[0][0]  # First positional argument
        self.assertEqual(len(call_args), 3)
        
        # Verify telemetry object properties
        for telemetry in call_args:
            self.assertEqual(telemetry.devi, self.device)
            self.assertIn(telemetry.key, ['temp', 'batt', 'humidity'])
            self.assertEqual(telemetry.datetime, self.device.lupd)
            
    @patch('telemetry.scripts.Telemetry.objects.bulk_create')
    def test_batch_generate_telemetries_large_batch(self, mock_bulk_create):
        """Test batch_generate_telemetries with large dataset requiring batching"""
        # Create many telemetry keys
        telemetries = [f'sensor_{i}' for i in range(250)]
        
        # Add these keys to device attr for testing
        for key in telemetries:
            self.device.attr["client"][key] = f"value_{key}"
            
        batch_generate_telemetries(self.device, telemetries, batch_size=100)
        
        # Should call bulk_create 3 times (100, 100, 50)
        self.assertEqual(mock_bulk_create.call_count, 3)
        
    @patch('telemetry.scripts.logger')
    @patch('telemetry.scripts.Telemetry.objects.bulk_create')
    def test_batch_generate_telemetries_success_logging(self, mock_bulk_create, mock_logger):
        """Test batch_generate_telemetries logs success"""
        telemetries = ['temp', 'batt']
        
        batch_generate_telemetries(self.device, telemetries)
        
        mock_logger.info.assert_called_once()
        log_message = mock_logger.info.call_args[0][0]
        self.assertIn(f"Created 2 telemetry records for device {self.device.id}", log_message)
        
    @patch('telemetry.scripts.logger')
    @patch('telemetry.scripts.Telemetry.objects.bulk_create')
    def test_batch_generate_telemetries_error_handling(self, mock_bulk_create, mock_logger):
        """Test batch_generate_telemetries handles errors gracefully"""
        mock_bulk_create.side_effect = Exception("Database error")
        
        telemetries = ['temp', 'batt']
        
        # Should not raise exception
        batch_generate_telemetries(self.device, telemetries)
        
        mock_logger.error.assert_called_once()
        log_message = mock_logger.error.call_args[0][0]
        self.assertIn(f"Error generating telemetries for device {self.device.id}", log_message)
        
    def test_batch_generate_telemetries_empty_list(self):
        """Test batch_generate_telemetries with empty telemetries list"""
        with patch('telemetry.scripts.Telemetry.objects.bulk_create') as mock_bulk_create:
            batch_generate_telemetries(self.device, [])
            
            # Should not call bulk_create for empty list
            mock_bulk_create.assert_not_called()


@tag('integration')
class TelemetryScriptsIntegrationTests(BaseTelemetryScriptsTest):
    """Integration tests for telemetry scripts with real database operations"""
    
    def test_generate_telemetries_database_integration(self):
        """Test generate_telemetries with real database operations"""
        telemetries = ['temp', 'batt', 'humidity']
        
        # Verify no telemetry records exist initially
        initial_count = Telemetry.objects.filter(devi=self.device).count()
        self.assertEqual(initial_count, 0)
        
        generate_telemetries(self.device, telemetries)
        
        # Verify telemetry records were created
        final_count = Telemetry.objects.filter(devi=self.device).count()
        self.assertEqual(final_count, 3)
        
        # Verify record contents
        temp_record = Telemetry.objects.get(devi=self.device, key='temp')
        self.assertEqual(temp_record.value, str(self.device.attr["client"]["temp"]))
        self.assertEqual(temp_record.datetime, self.device.lupd)
        
    def test_batch_generate_telemetries_database_integration(self):
        """Test batch_generate_telemetries with real database operations"""
        telemetries = ['temp', 'batt', 'humidity', 'pressure']
        
        # Verify no telemetry records exist initially
        initial_count = Telemetry.objects.filter(devi=self.device).count()
        self.assertEqual(initial_count, 0)
        
        batch_generate_telemetries(self.device, telemetries)
        
        # Verify telemetry records were created
        final_count = Telemetry.objects.filter(devi=self.device).count()
        self.assertEqual(final_count, 4)
        
        # Verify all records have correct device and datetime
        records = Telemetry.objects.filter(devi=self.device)
        for record in records:
            self.assertEqual(record.devi, self.device)
            self.assertEqual(record.datetime, self.device.lupd)