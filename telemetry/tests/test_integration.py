"""
Integration tests for telemetry module

Use --settings=app.test_settings for running tests

example:
python manage.py test telemetry.tests.test_integration --settings=app.test_settings
"""

import json
import threading
import time
from django.test import TestCase, Client, tag
from django.urls import reverse
from django.contrib.auth.models import User
from django.utils import timezone
from django.db import transaction, connection
from datetime import datetime, timedelta
from unittest.mock import patch, Mock

from telemetry.models import Telemetry
from telemetry.scripts import generate_telemetries, batch_generate_telemetries, process_sensory
from telemetry.views import TelemetryView, get_entries
from device_manager.models import Device
from fields.models import Field


class BaseTelemetryIntegrationTest(TestCase):
    """Base class for telemetry integration tests with common setup"""
    
    def setUp(self):
        """Set up test data"""
        # Create test user
        self.user = User.objects.create_user(
            username='testuser',
            password='testpassword',
            email='<EMAIL>'
        )
        
        # Create test field
        self.field = Field.objects.create(
            name="Test Field",
            cord=[{"lat": 0, "lng": 0}],
            colr="#FF0000",
            covr=10.0,
            loca="Test Location"
        )
        
        # Create test devices
        self.device1 = Device.objects.create(
            name="Device 1",
            desc="First Test Device",
            euid="ABCDEF0123456789",
            stat="Online",
            temp=25,
            batt=80,
            chrg=False,
            actv=True,
            mntc=False,
            hidn=False,
            attr={
                "client": {
                    "temp": 25.5,
                    "batt": 80,
                    "humidity": 65,
                    "pressure": 1013.25
                }
            },
            type="Whiskers Node V1",
            aset="Battery",
            loca={"lati": 12.34, "long": 56.78, "alti": 100, "oofi": False, "plac": "Outdoor"},
            fild=self.field,
            offp=10,
            lupd=timezone.now()
        )
        
        self.device2 = Device.objects.create(
            name="Device 2",
            desc="Second Test Device",
            euid="FEDCBA9876543210",
            stat="Offline",
            temp=30,
            batt=60,
            chrg=True,
            actv=False,
            mntc=True,
            hidn=True,
            attr={
                "client": {
                    "temp": 30.0,
                    "batt": 60,
                    "humidity": 70,
                    "light": 500
                }
            },
            type="Whiskers Node V2",
            aset="Solar",
            loca={"lati": 23.45, "long": 67.89, "alti": 200, "oofi": True, "plac": "Indoor"},
            fild=self.field,
            offp=20,
            lupd=timezone.now()
        )
        
        self.client = Client()


@tag('integration')
class TelemetryDataFlowIntegrationTests(BaseTelemetryIntegrationTest):
    """Integration tests for telemetry data flow from creation to retrieval"""
    
    def test_complete_telemetry_data_flow(self):
        """Test complete flow from telemetry generation to web retrieval"""
        # Step 1: Generate telemetries using scripts
        telemetries = ['temp', 'batt', 'humidity']
        generate_telemetries(self.device1, telemetries)
        
        # Step 2: Verify data was created in database
        db_records = Telemetry.objects.filter(devi=self.device1)
        self.assertEqual(db_records.count(), 3)
        
        # Step 3: Retrieve data via web API
        self.client.login(username='testuser', password='testpassword')
        
        for key in telemetries:
            url = reverse('telemetry:get-entries', kwargs={
                'device_id': str(self.device1.id),
                'key': key
            })
            
            response = self.client.get(url)
            self.assertEqual(response.status_code, 200)
            
            data = json.loads(response.content)
            self.assertEqual(len(data), 1)
            self.assertEqual(data[0]['key'], key)
            
    def test_batch_telemetry_generation_and_retrieval(self):
        """Test batch telemetry generation and subsequent retrieval"""
        # Generate large batch of telemetries
        telemetries = [f'sensor_{i}' for i in range(50)]
        
        # Add sensors to device attr
        for key in telemetries:
            self.device1.attr["client"][key] = f"value_{key}"
            
        batch_generate_telemetries(self.device1, telemetries, batch_size=10)
        
        # Verify all records were created
        total_records = Telemetry.objects.filter(devi=self.device1).count()
        self.assertEqual(total_records, 50)
        
        # Test retrieval of specific sensors
        self.client.login(username='testuser', password='testpassword')
        
        test_keys = ['sensor_0', 'sensor_25', 'sensor_49']
        for key in test_keys:
            url = reverse('telemetry:get-entries', kwargs={
                'device_id': str(self.device1.id),
                'key': key
            })
            
            response = self.client.get(url)
            data = json.loads(response.content)
            
            self.assertEqual(len(data), 1)
            self.assertEqual(data[0]['key'], key)
            self.assertEqual(data[0]['value'], f"value_{key}")
            
    def test_multi_device_telemetry_isolation(self):
        """Test that telemetry data is properly isolated between devices"""
        # Generate telemetries for both devices with same keys
        telemetries = ['temp', 'batt']
        
        generate_telemetries(self.device1, telemetries)
        generate_telemetries(self.device2, telemetries)
        
        # Verify data isolation
        device1_records = Telemetry.objects.filter(devi=self.device1)
        device2_records = Telemetry.objects.filter(devi=self.device2)
        
        self.assertEqual(device1_records.count(), 2)
        self.assertEqual(device2_records.count(), 2)
        
        # Test web API isolation
        self.client.login(username='testuser', password='testpassword')
        
        # Get temp data for device1
        url1 = reverse('telemetry:get-entries', kwargs={
            'device_id': str(self.device1.id),
            'key': 'temp'
        })
        response1 = self.client.get(url1)
        data1 = json.loads(response1.content)
        
        # Get temp data for device2
        url2 = reverse('telemetry:get-entries', kwargs={
            'device_id': str(self.device2.id),
            'key': 'temp'
        })
        response2 = self.client.get(url2)
        data2 = json.loads(response2.content)
        
        # Verify different values
        self.assertEqual(data1[0]['value'], str(self.device1.attr["client"]["temp"]))
        self.assertEqual(data2[0]['value'], str(self.device2.attr["client"]["temp"]))
        self.assertNotEqual(data1[0]['value'], data2[0]['value'])
        
    def test_sensory_processing_to_telemetry_generation(self):
        """Test integration between sensory processing and telemetry generation"""
        # Process sensory data
        sensory_data = {
            "batt": 95,
            "temp": 28,
            "lati": 40.7128,
            "long": -74.0060
        }
        
        process_sensory(self.device1, sensory_data)
        
        # Update device attr to reflect processed data
        self.device1.attr["client"]["batt"] = 95
        self.device1.attr["client"]["temp"] = 28
        
        # Generate telemetries based on processed data
        telemetries = ['batt', 'temp']
        generate_telemetries(self.device1, telemetries)
        
        # Verify telemetries reflect processed sensory data
        batt_record = Telemetry.objects.get(devi=self.device1, key='batt')
        temp_record = Telemetry.objects.get(devi=self.device1, key='temp')
        
        self.assertEqual(batt_record.value, '95')
        self.assertEqual(temp_record.value, '28')
        
        # Verify device attributes were updated
        self.assertEqual(self.device1.batt, 95)
        self.assertEqual(self.device1.temp, 28)
        self.assertEqual(self.device1.loca["lati"], 40.7128)
        self.assertEqual(self.device1.loca["long"], -74.0060)


@tag('integration')
class TelemetryWebIntegrationTests(BaseTelemetryIntegrationTest):
    """Integration tests for telemetry web interface components"""
    
    def test_viewer_to_api_integration(self):
        """Test integration between viewer page and API endpoint"""
        # Create telemetry data
        for i in range(10):
            Telemetry.objects.create(
                devi=self.device1,
                key='temperature',
                value=str(20 + i),
                datetime=timezone.now() + timedelta(minutes=i)
            )
            
        self.client.login(username='testuser', password='testpassword')
        
        # Access viewer page
        viewer_url = reverse('telemetry:viewer', kwargs={
            'device_id': str(self.device1.id),
            'key': 'temperature',
            'title': 'Temperature Monitor'
        })
        
        viewer_response = self.client.get(viewer_url)
        self.assertEqual(viewer_response.status_code, 200)
        
        # Verify context data is passed correctly
        self.assertEqual(viewer_response.context['device_id'], str(self.device1.id))
        self.assertEqual(viewer_response.context['key'], 'temperature')
        self.assertEqual(viewer_response.context['title'], 'Temperature Monitor')
        
        # Access corresponding API endpoint
        api_url = reverse('telemetry:get-entries', kwargs={
            'device_id': str(self.device1.id),
            'key': 'temperature'
        })
        
        api_response = self.client.get(api_url)
        self.assertEqual(api_response.status_code, 200)
        
        # Verify API returns correct data
        data = json.loads(api_response.content)
        self.assertEqual(len(data), 10)
        
        # Verify data ordering (should be by datetime)
        values = [int(record['value']) for record in data]
        self.assertEqual(values, list(range(20, 30)))
        
    def test_authentication_integration(self):
        """Test authentication integration across all telemetry endpoints"""
        # Create test data
        Telemetry.objects.create(
            devi=self.device1,
            key='test_sensor',
            value='test_value',
            datetime=timezone.now()
        )
        
        # Test without authentication
        viewer_url = reverse('telemetry:viewer', kwargs={
            'device_id': str(self.device1.id),
            'key': 'test_sensor',
            'title': 'Test'
        })
        
        api_url = reverse('telemetry:get-entries', kwargs={
            'device_id': str(self.device1.id),
            'key': 'test_sensor'
        })
        
        # Both should redirect to login
        viewer_response = self.client.get(viewer_url)
        api_response = self.client.get(api_url)
        
        self.assertEqual(viewer_response.status_code, 302)
        self.assertEqual(api_response.status_code, 302)
        
        # Test with authentication
        self.client.login(username='testuser', password='testpassword')
        
        viewer_response = self.client.get(viewer_url)
        api_response = self.client.get(api_url)
        
        self.assertEqual(viewer_response.status_code, 200)
        self.assertEqual(api_response.status_code, 200)
        
@tag('integration')
class TelemetryDatabaseIntegrationTests(BaseTelemetryIntegrationTest):
    """Integration tests for telemetry database operations"""
    
    def test_large_dataset_operations(self):
        """Test operations with large telemetry datasets"""
        # Create large dataset
        telemetry_records = []
        base_time = timezone.now()
        
        for i in range(1000):
            telemetry_records.append(Telemetry(
                devi=self.device1,
                key=f'sensor_{i % 10}',
                value=str(i * 0.1),
                datetime=base_time + timedelta(seconds=i)
            ))
            
        # Bulk create
        created_records = Telemetry.objects.bulk_create(telemetry_records)
        self.assertEqual(len(created_records), 1000)
        
        # Test filtering performance
        temp_records = Telemetry.objects.filter(
            devi=self.device1,
            key='sensor_0'
        ).order_by('datetime')
        
        self.assertEqual(temp_records.count(), 100)  # Every 10th record
        
        # Test time-based filtering
        mid_time = base_time + timedelta(seconds=500)
        recent_records = Telemetry.objects.filter(
            devi=self.device1,
            datetime__gte=mid_time
        )
        
        self.assertEqual(recent_records.count(), 500)
        
    def test_transaction_integrity(self):
        """Test transaction integrity in telemetry operations"""
        initial_count = Telemetry.objects.count()
        
        try:
            with transaction.atomic():
                # Create some records
                for i in range(5):
                    Telemetry.objects.create(
                        devi=self.device1,
                        key='transaction_test',
                        value=str(i),
                        datetime=timezone.now()
                    )
                    
                # Force an error
                raise Exception("Simulated error")
                
        except Exception:
            pass
            
        # No records should have been created due to rollback
        final_count = Telemetry.objects.count()
        self.assertEqual(final_count, initial_count)
        
    def test_cascade_delete_integration(self):
        """Test cascade delete behavior with related models"""
        # Create telemetry records
        telemetries = []
        for i in range(5):
            telemetry = Telemetry.objects.create(
                devi=self.device1,
                key=f'cascade_test_{i}',
                value=str(i),
                datetime=timezone.now()
            )
            telemetries.append(telemetry)
            
        # Verify records exist
        self.assertEqual(Telemetry.objects.filter(devi=self.device1).count(), 5)
        
        # Delete device
        device_id = self.device1.id
        self.device1.delete()
        
        # Verify telemetry records were cascade deleted
        remaining_records = Telemetry.objects.filter(devi=device_id)
        self.assertEqual(remaining_records.count(), 0)
        
    def test_database_constraints_integration(self):
        """Test database constraints and validation integration"""
        # Test foreign key constraint
        with self.assertRaises(Exception):
            Telemetry.objects.create(
                devi_id=99999,  # Non-existent device
                key='constraint_test',
                value='test',
                datetime=timezone.now()
            )
            with connection.cursor() as cursor:
                cursor.execute("SET CONSTRAINTS ALL IMMEDIATE")
            
        # Test field length constraints
        long_key = 'a' * 256  # Exceeds max_length
        long_value = 'b' * 256  # Exceeds max_length
        
        # These should raise validation errors
        telemetry1 = Telemetry(
            devi=self.device1,
            key=long_key,
            value='test',
            datetime=timezone.now()
        )
        
        telemetry2 = Telemetry(
            devi=self.device1,
            key='test',
            value=long_value,
            datetime=timezone.now()
        )
        
        with self.assertRaises(Exception):
            telemetry1.full_clean()
            
        with self.assertRaises(Exception):
            telemetry2.full_clean()
