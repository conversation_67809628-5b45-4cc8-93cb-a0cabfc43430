import sys
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.shortcuts import get_object_or_404, render
from django.utils.decorators import method_decorator
from django.contrib.auth.mixins import LoginRequiredMixin
from django.views.generic import View
from django.contrib.contenttypes.models import ContentType
from telemetry.models import Telemetry
from device_manager.models import Device


# Create your views here.
@method_decorator(login_required, name="dispatch")
class TelemetryView(LoginRequiredMixin, View):
    template_name = "telemetry/viewer.html"  # Replace with your template name

    def get(self, request, *args, **kwargs):
        key = kwargs["key"]
        title = kwargs["title"]
        device_id = kwargs["device_id"]
        device = get_object_or_404(Device, id=device_id)

        context = {"key": key, "title": title, "device": device}

        return render(request, self.template_name, context)


@login_required
def get_entries(request, device_id, key):

    queryset = Telemetry.objects.filter(devi=device_id, key=key).order_by("datetime")

    telemetry_data = [
        {
            "key": telemetry.key,
            "value": telemetry.value,
            "datetime": telemetry.datetime,
        }
        for telemetry in queryset
    ]

    return JsonResponse(telemetry_data, safe=False)
