from functools import wraps
from django.http import HttpResponseForbidden
from django.contrib.auth.decorators import login_required
from django.core.exceptions import PermissionDenied
from accounts.models import UserProfile


def local_only(view_func):
    @wraps(view_func)
    def wrapped_view(request, *args, **kwargs):
        # Check if the request's IP is in the local IP range, by checking if it starts with 172, 192 or 10
        if not str(request.META.get("REMOTE_ADDR")).startswith("172."):
            print("Access Denied")
            return HttpResponseForbidden("Access Denied")
        return view_func(request, *args, **kwargs)

    return wrapped_view


def get_user_role(user):
    """Get the role of a user from their profile"""
    if not user.is_authenticated:
        return None
    try:
        return user.userprofile.role
    except UserProfile.DoesNotExist:
        return None


def has_role_permission(user, required_roles):
    """Check if user has any of the required roles"""
    if not user.is_authenticated:
        return False

    user_role = get_user_role(user)
    if not user_role:
        return False

    # Convert single role to list for consistency
    if isinstance(required_roles, str):
        required_roles = [required_roles]

    return user_role in required_roles


def admin_required(view_func):
    """Decorator that requires Admin role"""
    @wraps(view_func)
    @login_required
    def wrapped_view(request, *args, **kwargs):
        if not has_role_permission(request.user, 'Admin'):
            raise PermissionDenied("Admin access required")
        return view_func(request, *args, **kwargs)
    return wrapped_view


def admin_or_owner_required(view_func):
    """Decorator that requires Admin or Owner role"""
    @wraps(view_func)
    @login_required
    def wrapped_view(request, *args, **kwargs):
        if not has_role_permission(request.user, ['Admin', 'Owner']):
            raise PermissionDenied("Admin or Owner access required")
        return view_func(request, *args, **kwargs)
    return wrapped_view


def authenticated_required(view_func):
    """Decorator that requires any authenticated user (Admin, Owner, or User)"""
    @wraps(view_func)
    @login_required
    def wrapped_view(request, *args, **kwargs):
        if not has_role_permission(request.user, ['Admin', 'Owner', 'User']):
            raise PermissionDenied("Authentication required")
        return view_func(request, *args, **kwargs)
    return wrapped_view
