from django.contrib.auth.mixins import LoginRequiredMixin, UserPassesTestMixin
from django.core.exceptions import PermissionDenied
from .decorators import get_user_role, has_role_permission


class RoleRequiredMixin(LoginRequiredMixin, UserPassesTestMixin):
    """Base mixin for role-based access control"""
    required_roles = []
    
    def test_func(self):
        """Test if user has required role"""
        return has_role_permission(self.request.user, self.required_roles)
    
    def handle_no_permission(self):
        """Handle permission denied"""
        if not self.request.user.is_authenticated:
            return super().handle_no_permission()
        raise PermissionDenied(f"Access denied. Required roles: {', '.join(self.required_roles)}")


class AdminRequiredMixin(RoleRequiredMixin):
    """Mixin that requires Admin role"""
    required_roles = ['Admin']


class AdminOrOwnerRequiredMixin(RoleRequiredMixin):
    """Mixin that requires Admin or Owner role"""
    required_roles = ['Admin', 'Owner']


class AuthenticatedRequiredMixin(RoleRequiredMixin):
    """Mixin that requires any authenticated user with a valid role"""
    required_roles = ['Admin', 'Owner', 'User']


class DeviceAccessMixin:
    """Mixin to check if user has access to a specific device"""
    
    def dispatch(self, request, *args, **kwargs):
        """Check device access before dispatching"""
        # Get the device object
        device = self.get_object()
        user = request.user
        
        # Admin users have access to all devices
        if has_role_permission(user, 'Admin'):
            return super().dispatch(request, *args, **kwargs)
        
        # Owner and User roles need to have device assigned to them
        if has_role_permission(user, ['Owner', 'User']):
            if user.userprofile.devs.filter(id=device.id).exists():
                return super().dispatch(request, *args, **kwargs)
        
        raise PermissionDenied("You don't have access to this device")


class FieldAccessMixin:
    """Mixin to check if user has access to field operations"""
    
    def dispatch(self, request, *args, **kwargs):
        """Check field access before dispatching"""
        user = request.user
        
        # Admin users have full access to fields
        if has_role_permission(user, 'Admin'):
            return super().dispatch(request, *args, **kwargs)
        
        # Owner and User roles can view fields but not modify them
        # This mixin should be combined with appropriate role mixins
        return super().dispatch(request, *args, **kwargs)
