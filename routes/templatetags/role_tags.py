from django import template
from routes.decorators import get_user_role, has_role_permission

register = template.Library()


@register.simple_tag
def user_role(user):
    """Get the role of the current user"""
    return get_user_role(user)


@register.simple_tag
def has_role(user, roles):
    """Check if user has any of the specified roles"""
    if isinstance(roles, str):
        roles = [roles]
    return has_role_permission(user, roles)


@register.simple_tag
def is_admin(user):
    """Check if user is Admin"""
    return has_role_permission(user, 'Admin')


@register.simple_tag
def is_admin_or_owner(user):
    """Check if user is Admin or Owner"""
    return has_role_permission(user, ['Admin', 'Owner'])


@register.simple_tag
def can_edit_entities(user):
    """Check if user can edit, add, delete entities (Admin only)"""
    return has_role_permission(user, 'Admin')


@register.simple_tag
def can_reset_status(user):
    """Check if user can reset status (Admin or Owner)"""
    return has_role_permission(user, ['Admin', 'Owner'])


@register.simple_tag
def can_enable_maintenance(user):
    """Check if user can enable maintenance mode (Admin or Owner)"""
    return has_role_permission(user, ['Admin', 'Owner'])


@register.simple_tag
def can_view_assigned_assets(user):
    """Check if user can view assigned assets (All roles)"""
    return has_role_permission(user, ['Admin', 'Owner', 'User'])


@register.simple_tag
def can_view_fields(user):
    """Check if user can view fields (All roles)"""
    return has_role_permission(user, ['Admin', 'Owner', 'User'])


@register.simple_tag
def can_edit_fields(user):
    """Check if user can edit/modify fields (Admin only)"""
    return has_role_permission(user, 'Admin')


@register.simple_tag
def can_access_packet_analyzer(user):
    """Check if user can access packet analyzer (Admin only)"""
    return has_role_permission(user, 'Admin')


@register.simple_tag
def can_view_users_list(user):
    """Check if user can view users list (Admin or Owner)"""
    return has_role_permission(user, ['Admin', 'Owner'])


@register.simple_tag
def can_access_settings(user):
    """Check if user can access settings page (Admin only)"""
    return has_role_permission(user, 'Admin')


@register.filter
def has_device_access(user, device):
    """Check if user has access to a specific device"""
    if not user.is_authenticated:
        return False
    
    # Admin users have access to all devices
    if has_role_permission(user, 'Admin'):
        return True
    
    # Owner and User roles need to have device assigned to them
    if has_role_permission(user, ['Owner', 'User']):
        try:
            return user.userprofile.devs.filter(id=device.id).exists()
        except:
            return False
    
    return False
